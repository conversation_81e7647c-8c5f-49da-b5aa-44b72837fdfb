# -*- coding: utf-8 -*-
"""
Management command để tạo các roles theo chức năng dựa trên sidebar
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from permissions.models import Role, Permission


class Command(BaseCommand):
    help = '<PERSON><PERSON><PERSON> cá<PERSON> roles theo chức năng dựa trên sidebar'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='<PERSON><PERSON><PERSON> t<PERSON>t cả roles hiện có trước khi tạo mới',
        )

    def handle(self, *args, **options):
        clear_existing = options['clear']

        if clear_existing:
            self.stdout.write('<PERSON><PERSON><PERSON> t<PERSON><PERSON> cả roles hiện có...')
            Role.objects.all().delete()

        self.stdout.write(
            self.style.SUCCESS('<PERSON><PERSON><PERSON> đầu tạo roles theo chức năng...')
        )

        try:
            with transaction.atomic():
                # 1. Super Admin - <PERSON><PERSON><PERSON> cả quyền
                self.create_super_admin_role()
                
                # 2. <PERSON><PERSON> thống - <PERSON><PERSON><PERSON><PERSON> lý hệ thống nhưng không có quyền superuser
                self.create_system_admin_role()
                
                # 3. Quản lý Y tế - Quản lý danh mục y tế và XML
                self.create_medical_manager_role()
                
                # 4. Quản lý Thiết bị - Quản lý thiết bị và giám sát
                self.create_device_manager_role()
                
                # 5. Quản lý Nhân sự - Quản lý người dùng và phân quyền
                self.create_hr_manager_role()
                
                # 6. Bác sĩ - Xem thông tin y tế và báo cáo
                self.create_doctor_role()
                
                # 7. Điều dưỡng - Quản lý lịch trực và thông tin cơ bản
                self.create_nurse_role()
                
                # 8. Nhân viên IT - Quản lý thiết bị và hệ thống
                self.create_it_staff_role()
                
                # 9. Nhân viên Hành chính - Email và báo cáo cơ bản
                self.create_admin_staff_role()

                self.stdout.write('\n' + '='*50)
                self.stdout.write(
                    self.style.SUCCESS('Hoàn thành tạo roles theo chức năng!')
                )
                self.stdout.write(f'Tổng số roles: {Role.objects.count()}')

        except Exception as e:
            raise CommandError(f'Lỗi khi tạo roles: {str(e)}')

    def create_super_admin_role(self):
        """Tạo role Super Admin với tất cả quyền"""
        role, created = Role.objects.get_or_create(
            name="Super Admin",
            defaults={'description': "Quản trị viên tối cao - có tất cả quyền trong hệ thống"}
        )
        
        # Gán tất cả quyền
        all_permissions = Permission.objects.all()
        role.custom_permissions.set(all_permissions)
        
        self.stdout.write(f'✓ Tạo role: {role.name} với {all_permissions.count()} quyền')

    def create_system_admin_role(self):
        """Tạo role Admin Hệ thống"""
        role, created = Role.objects.get_or_create(
            name="Admin Hệ thống",
            defaults={'description': "Quản trị viên hệ thống - quản lý người dùng, thiết bị, phân quyền"}
        )
        
        # Quyền quản lý hệ thống
        permissions = Permission.objects.filter(
            module__in=['users', 'devices', 'permissions', 'departments', 'system']
        ).exclude(
            code__in=['users.delete_superuser', 'permissions.delete_all_roles']
        )
        
        role.custom_permissions.set(permissions)
        self.stdout.write(f'✓ Tạo role: {role.name} với {permissions.count()} quyền')

    def create_medical_manager_role(self):
        """Tạo role Quản lý Y tế"""
        role, created = Role.objects.get_or_create(
            name="Quản lý Y tế",
            defaults={'description': "Quản lý danh mục y tế, XML, hồ sơ bệnh án"}
        )
        
        # Quyền quản lý y tế
        permissions = Permission.objects.filter(
            module__in=['danhmucbv', 'danhmuc130', 'xml4750', 'hoso', 'reports']
        )
        
        role.custom_permissions.set(permissions)
        self.stdout.write(f'✓ Tạo role: {role.name} với {permissions.count()} quyền')

    def create_device_manager_role(self):
        """Tạo role Quản lý Thiết bị"""
        role, created = Role.objects.get_or_create(
            name="Quản lý Thiết bị",
            defaults={'description': "Quản lý thiết bị, giám sát hoạt động, cảnh báo"}
        )
        
        # Quyền quản lý thiết bị
        permissions = Permission.objects.filter(
            module__in=['devices', 'system']
        ).union(
            Permission.objects.filter(code__startswith='users.view_')
        )
        
        role.custom_permissions.set(permissions)
        self.stdout.write(f'✓ Tạo role: {role.name} với {permissions.count()} quyền')

    def create_hr_manager_role(self):
        """Tạo role Quản lý Nhân sự"""
        role, created = Role.objects.get_or_create(
            name="Quản lý Nhân sự",
            defaults={'description': "Quản lý người dùng, phân quyền, lịch trực"}
        )
        
        # Quyền quản lý nhân sự
        permissions = Permission.objects.filter(
            module__in=['users', 'permissions', 'departments', 'schedules', 'emails']
        ).exclude(
            code__in=['users.delete_superuser', 'permissions.delete_all_roles']
        )
        
        role.custom_permissions.set(permissions)
        self.stdout.write(f'✓ Tạo role: {role.name} với {permissions.count()} quyền')

    def create_doctor_role(self):
        """Tạo role Bác sĩ"""
        role, created = Role.objects.get_or_create(
            name="Bác sĩ",
            defaults={'description': "Xem thông tin y tế, hồ sơ bệnh án, báo cáo"}
        )

        # Quyền của bác sĩ
        permissions = Permission.objects.filter(
            code__in=[
                # Xem danh mục y tế
                'danhmucbv.view', 'danhmuc130.view',
                # Xem và quản lý hồ sơ
                'hoso.view', 'hoso.add', 'hoso.edit',
                # Xem XML
                'xml4750.view',
                # Báo cáo
                'reports.view', 'reports.create',
                # Email
                'emails.view', 'emails.send',
                # Lịch trực
                'schedules.view',
                # Luyện tập
                'typingpractice.view', 'typingpractice.take_exam'
            ]
        )

        role.custom_permissions.set(permissions)
        self.stdout.write(f'✓ Tạo role: {role.name} với {permissions.count()} quyền')

    def create_nurse_role(self):
        """Tạo role Điều dưỡng"""
        role, created = Role.objects.get_or_create(
            name="Điều dưỡng",
            defaults={'description': "Quản lý lịch trực, xem thông tin cơ bản"}
        )

        # Quyền của điều dưỡng
        permissions = Permission.objects.filter(
            code__in=[
                # Lịch trực
                'schedules.view', 'schedules.add', 'schedules.edit',
                # Xem danh mục cơ bản
                'danhmucbv.view',
                # Xem hồ sơ
                'hoso.view',
                # Email
                'emails.view', 'emails.send',
                # Luyện tập
                'typingpractice.view', 'typingpractice.take_exam'
            ]
        )

        role.custom_permissions.set(permissions)
        self.stdout.write(f'✓ Tạo role: {role.name} với {permissions.count()} quyền')

    def create_it_staff_role(self):
        """Tạo role Nhân viên IT"""
        role, created = Role.objects.get_or_create(
            name="Nhân viên IT",
            defaults={'description': "Quản lý thiết bị, hệ thống, giám sát"}
        )

        # Quyền của nhân viên IT
        permissions = Permission.objects.filter(
            module__in=['devices', 'system']
        ).union(
            Permission.objects.filter(
                code__in=[
                    'users.view', 'emails.view', 'emails.send',
                    'typingpractice.view', 'typingpractice.manage'
                ]
            )
        )

        role.custom_permissions.set(permissions)
        self.stdout.write(f'✓ Tạo role: {role.name} với {permissions.count()} quyền')

    def create_admin_staff_role(self):
        """Tạo role Nhân viên Hành chính"""
        role, created = Role.objects.get_or_create(
            name="Nhân viên Hành chính",
            defaults={'description': "Email nội bộ, báo cáo cơ bản, luyện tập gõ phím"}
        )

        # Quyền của nhân viên hành chính
        permissions = Permission.objects.filter(
            code__in=[
                # Email
                'emails.view', 'emails.send', 'emails.manage_folders',
                # Báo cáo cơ bản
                'reports.view',
                # Luyện tập
                'typingpractice.view', 'typingpractice.take_exam',
                # Xem thông tin cơ bản
                'users.view_profile', 'departments.view'
            ]
        )

        role.custom_permissions.set(permissions)
        self.stdout.write(f'✓ Tạo role: {role.name} với {permissions.count()} quyền')
