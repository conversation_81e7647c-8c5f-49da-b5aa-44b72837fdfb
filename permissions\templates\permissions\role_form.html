{% extends 'layouts/base.html' %}

{% block title %}{{ title }} | Hospital Manager{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item"><a href="{% url 'permissions:role_list' %}">Quản lý vai trò</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-md-12">
    <div class="card card-primary">
      <div class="card-header">
        <h3 class="card-title">Thông tin vai trò</h3>
      </div>
      <!-- /.card-header -->
      <!-- form start -->
      <form role="form" method="post">
        {% csrf_token %}
        <div class="card-body">
          {% if form.non_field_errors %}
          <div class="alert alert-danger">
            {% for error in form.non_field_errors %}
              {{ error }}
            {% endfor %}
          </div>
          {% endif %}

          {% if messages %}
          <div class="alert alert-success">
            {% for message in messages %}
              {{ message }}
            {% endfor %}
          </div>
          {% endif %}

          <div class="row">
            <!-- Thông tin cơ bản - 1/3 bên trái -->
            <div class="col-md-4">
              <div class="form-group">
                <label for="{{ form.name.id_for_label }}">Tên vai trò</label>
                {{ form.name }}
                {% if form.name.errors %}
                <div class="text-danger">
                  {% for error in form.name.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>

              <div class="form-group">
                <label for="{{ form.description.id_for_label }}">Mô tả</label>
                {{ form.description }}
                {% if form.description.errors %}
                <div class="text-danger">
                  {% for error in form.description.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>

              {% if not is_edit %}
              <div class="form-group">
                <label>Tạo từ mẫu</label>
                <div class="card">
                  <div class="card-header bg-light">
                    <h3 class="card-title">Chọn mẫu vai trò</h3>
                  </div>
                  <div class="card-body">
                    <div class="form-group">
                      <select id="role-template" class="form-control">
                        <option value="">-- Chọn mẫu vai trò --</option>
                        <option value="superadmin">Super Admin</option>
                        <option value="admin">Admin</option>
                        <option value="department_manager">Quản lý khoa phòng</option>
                        <option value="staff">Nhân viên</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <button type="button" id="load-template" class="btn btn-info btn-sm">
                        <i class="fas fa-sync-alt mr-1"></i> Tải quyền từ mẫu
                      </button>
                      <a href="{% url 'permissions:role_template_manager' %}" class="btn btn-outline-secondary btn-sm ml-2">
                        <i class="fas fa-cogs mr-1"></i> Quản lý mẫu
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              {% endif %}
            </div>

            <!-- Quyền - 2/3 bên phải -->
            <div class="col-md-8">
              <div class="form-group">
                <label>Quyền</label>
                <div class="card">
                  <div class="card-header">
                    <h3 class="card-title">Chọn quyền cho vai trò</h3>
                  </div>
                  <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    <div class="row">
                      {% if grouped_permissions %}
                        {% for module_name, permissions in grouped_permissions.items %}
                        <div class="col-md-12 mb-3">
                          <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5>{{ module_name|title }}</h5>
                            <div class="form-check">
                              <input type="checkbox" class="form-check-input check-module" id="check_module_{{ module_name|slugify }}" data-module="{{ module_name|slugify }}">
                              <label class="form-check-label" for="check_module_{{ module_name|slugify }}">
                                <strong>Chọn tất cả</strong>
                              </label>
                            </div>
                          </div>
                          <div class="row">
                            {% for permission in permissions %}
                            <div class="col-md-6">
                              <div class="form-check">
                                <input type="checkbox" class="form-check-input permission-checkbox" id="permission_{{ permission.id }}"
                                       name="custom_permissions" value="{{ permission.id }}" data-module="{{ module_name|slugify }}"
                                       {% if permission.id in selected_permissions %}checked{% endif %}>
                                <label class="form-check-label" for="permission_{{ permission.id }}">
                                  <strong>{{ permission.code }}</strong><br>
                                  <small class="text-muted">{{ permission.name }}</small>
                                </label>
                              </div>
                            </div>
                            {% endfor %}
                          </div>
                        </div>
                        {% endfor %}
                      {% else %}
                      <div class="col-md-12">
                        <div class="alert alert-warning">
                          <i class="fas fa-exclamation-triangle"></i>
                          <strong>Chưa có quyền nào được định nghĩa!</strong><br>
                          Vui lòng chạy lệnh <code>python manage.py sync_permissions</code> để tạo permissions từ danh sách định nghĩa.
                        </div>
                      </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
                {% if form.custom_permissions.errors %}
                <div class="text-danger">
                  {% for error in form.custom_permissions.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
        <!-- /.card-body -->

        <div class="card-footer">
          <button type="submit" class="btn btn-primary">Lưu</button>
          <a href="{% url 'permissions:role_list' %}" class="btn btn-default">Hủy</a>
        </div>
      </form>
    </div>
    <!-- /.card -->
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  $(function () {
    $('.select2').select2();

    // Xử lý tải quyền từ mẫu
    $('#load-template').on('click', function() {
      var templateType = $('#role-template').val();
      if (!templateType) {
        Swal.fire({
          icon: 'warning',
          title: 'Chưa chọn mẫu',
          text: 'Vui lòng chọn một mẫu vai trò để tải quyền.',
          confirmButtonText: 'OK'
        });
        return;
      }

      // Hiển thị thông báo xác nhận
      Swal.fire({
        icon: 'question',
        title: 'Xác nhận tải quyền',
        text: 'Việc này sẽ ghi đè lên các quyền đã chọn. Bạn có chắc chắn muốn tiếp tục?',
        showCancelButton: true,
        confirmButtonText: 'Đồng ý',
        cancelButtonText: 'Hủy'
      }).then((result) => {
        if (result.isConfirmed) {
          // Gọi API để lấy quyền từ mẫu
          $.ajax({
            url: '/permissions/api/get-template-permissions/',
            type: 'GET',
            data: {
              template_type: templateType
            },
            dataType: 'json',
            success: function(response) {
              if (response.success) {
                // Bỏ chọn tất cả các quyền hiện tại
                $('input[name="custom_permissions"]').prop('checked', false);

                // Chọn các quyền từ mẫu
                $.each(response.permissions, function(index, permissionId) {
                  $('#permission_' + permissionId).prop('checked', true);
                });

                // Cập nhật trạng thái checkbox module
                updateModuleCheckboxes();

                Swal.fire({
                  icon: 'success',
                  title: 'Thành công',
                  text: 'Đã tải quyền từ mẫu ' + response.template_name,
                  confirmButtonText: 'OK'
                });
              } else {
                Swal.fire({
                  icon: 'error',
                  title: 'Lỗi',
                  text: response.message || 'Không thể tải quyền từ mẫu.',
                  confirmButtonText: 'OK'
                });
              }
            },
            error: function() {
              Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: 'Đã xảy ra lỗi khi tải quyền từ mẫu.',
                confirmButtonText: 'OK'
              });
            }
          });
        }
      });
    });

    // Hàm cập nhật trạng thái checkbox module
    var updateModuleCheckboxes = function() {
      $('.check-module').each(function() {
        var module = $(this).data('module');
        var modulePermissions = $('.permission-checkbox[data-module="' + module + '"]');
        var checkedPermissions = modulePermissions.filter(':checked');
        $(this).prop('checked', modulePermissions.length === checkedPermissions.length && modulePermissions.length > 0);
      });

      // Cập nhật checkbox "Chọn tất cả"
      var allPermissions = $('.permission-checkbox');
      var allChecked = allPermissions.filter(':checked');
      $('#select-all-permissions').prop('checked', allPermissions.length === allChecked.length && allPermissions.length > 0);
    };

    // Thêm chức năng chọn/bỏ chọn tất cả
    var addSelectAllCheckbox = function() {
      // Thêm checkbox "Chọn tất cả" vào đầu danh sách quyền
      var selectAllHtml = `
        <div class="col-md-12 mb-3">
          <div class="form-check">
            <input type="checkbox" class="form-check-input" id="select-all-permissions">
            <label class="form-check-label" for="select-all-permissions">
              <strong>Chọn tất cả quyền</strong>
            </label>
          </div>
        </div>
      `;
      $('.card-body .row').first().prepend(selectAllHtml);

      // Xử lý sự kiện chọn/bỏ chọn tất cả
      $('#select-all-permissions').on('change', function() {
        $('.permission-checkbox').prop('checked', $(this).prop('checked'));
        updateModuleCheckboxes();
      });

      // Xử lý sự kiện chọn/bỏ chọn theo module
      $('.check-module').on('change', function() {
        var module = $(this).data('module');
        $('.permission-checkbox[data-module="' + module + '"]').prop('checked', $(this).prop('checked'));
        updateModuleCheckboxes();
      });

      // Cập nhật trạng thái khi các quyền thay đổi
      $('.permission-checkbox').on('change', function() {
        updateModuleCheckboxes();
      });

      // Cập nhật trạng thái ban đầu
      updateModuleCheckboxes();
    };

    // Thêm checkbox "Chọn tất cả" sau khi trang đã tải xong
    addSelectAllCheckbox();
  });
</script>
{% endblock %}
