from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout, update_session_auth_hash
from django.contrib.auth.models import User
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.http import HttpResponseRedirect, HttpResponse, JsonResponse
from django.urls import reverse
from django.views.decorators.csrf import csrf_exempt
from django.utils.dateparse import parse_date
from django.db.models import Q
from django.db import transaction

from departments.models import Department

from .forms import (
    UserLoginForm, UserRegistrationForm, UserEditForm, ProfileEditForm,
    UserPasswordChangeForm, AdminPasswordChangeForm, PasswordResetRequestForm,
    UserImportForm
)

from .models import UserActivity, UserProfile
from utils.ip_utils import get_client_ip
from permissions.models import Permission as CustomPermission
import openpyxl
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import datetime
import os
import uuid
import json
from .services import parse_excel_short_date, generate_username_from_name, parse_to_short_date

def login_view(request):
    if request.user.is_authenticated:
        return redirect('home')

    if request.method == 'POST':
        form = UserLoginForm(request.POST)
        if form.is_valid():
            username = form.cleaned_data['username']
            password = form.cleaned_data['password']
            remember = form.cleaned_data['remember']

            user = authenticate(request, username=username, password=password)

            if user is not None:
                login(request, user)

                # Log user activity
                UserActivity.objects.create(
                    user=user,
                    activity_type='login',
                    description='Đăng nhập thành công',
                    ip_address=get_client_ip(request)
                )

                # Set session expiry based on remember checkbox
                if not remember:
                    request.session.set_expiry(0)  # Session expires when browser closes

                next_url = request.GET.get('next', 'home')
                return redirect(next_url)
            else:
                form.add_error(None, 'Tên đăng nhập hoặc mật khẩu không đúng.')
    else:
        form = UserLoginForm()

    return render(request, 'users/login.html', {'form': form})

def logout_view(request):
    if request.user.is_authenticated:
        # Log user activity
        UserActivity.objects.create(
            user=request.user,
            activity_type='logout',
            description='Đăng xuất thành công',
            ip_address=get_client_ip(request)
        )

        logout(request)

    return redirect('users:login')

@login_required
def user_list(request):
    return render(request, 'users/list.html')

@login_required
def user_list_api(request):
    users = User.objects.select_related('profile__department', 'profile__position').all()

    data = []
    for user in users:
        profile = getattr(user, 'profile', None)

        row = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "is_active": user.is_active,
            "is_staff": user.is_staff,
            "is_superuser": user.is_superuser,
        }

        # Gộp các trường profile (không lồng)
        if profile:
            row.update({
                "ma_khoa": profile.ma_khoa,
                "gioi_tinh": profile.gioi_tinh,
                "ma_loai_kcb": profile.ma_loai_kcb,
                "chuc_danh_nn": profile.chuc_danh_nn,
                "vi_tri": profile.vi_tri,
                "practice_certificate": profile.practice_certificate,
                "ngay_cap_cchn": parse_to_short_date(profile.ngay_cap_cchn),
                "noi_cap_cchn": profile.noi_cap_cchn,
                "pham_vi_cm": profile.pham_vi_cm,
                "pham_vi_cm_bs": profile.pham_vi_cm_bs,
                "dvkt_khac": profile.dvkt_khac,
                "vb_phan_cong": profile.vb_phan_cong,
                "thoi_gian_dk": profile.thoi_gian_dk,
                "thoi_gian_ngay": profile.thoi_gian_ngay,
                "thoi_gian_tuan": profile.thoi_gian_tuan,
                "cskcb_khac": profile.cskcb_khac,
                "cskcb_cgkt": profile.cskcb_cgkt,
                "qd_cgkt": profile.qd_cgkt,
                "tu_ngay": parse_to_short_date(profile.tu_ngay),
                "den_ngay": parse_to_short_date(profile.den_ngay),
                "department": profile.department.name if profile.department else "",
                "position": profile.position.name if profile.position else "",
            })

        data.append(row)

    return JsonResponse(data, safe=False)

@login_required
def user_detail(request, user_id):
    user = get_object_or_404(User, id=user_id)
    return render(request, 'users/detail.html', {'user': user})



@login_required
def user_edit(request, user_id):
    # Xác định chế độ: tạo mới (user_id = 0) hoặc chỉnh sửa
    is_edit_mode = user_id != 0

    if is_edit_mode:
        user = get_object_or_404(User, id=user_id)
    else:
        user = None

    if request.method == 'POST':
        print(f"POST request received. Is edit mode: {is_edit_mode}")
        print(f"POST data keys: {list(request.POST.keys())}")

        if is_edit_mode:
            # Chế độ chỉnh sửa
            form = UserEditForm(request.POST, instance=user)
            profile_form = ProfileEditForm(request.POST, request.FILES, instance=user.profile)
        else:
            # Chế độ tạo mới
            form = UserRegistrationForm(request.POST)
            profile_form = ProfileEditForm(request.POST, request.FILES)

        if is_edit_mode:
            # Xử lý chỉnh sửa
            if form.is_valid() and profile_form.is_valid():
                try:
                    form.save()
                    profile_form.save()

                    # Set additional fields
                    user.is_staff = 'is_staff' in request.POST
                    user.is_superuser = 'is_superuser' in request.POST
                    user.is_active = 'is_active' in request.POST

                    # Cập nhật quyền riêng lẻ của người dùng (custom permissions)
                    custom_permissions = request.POST.getlist('custom_permissions')
                    from permissions.models import UserPermission

                    # Xóa tất cả quyền riêng lẻ hiện tại
                    UserPermission.objects.filter(user=user).delete()

                    # Thêm quyền riêng lẻ mới
                    if custom_permissions:
                        permissions = CustomPermission.objects.filter(id__in=custom_permissions)
                        for permission in permissions:
                            UserPermission.objects.create(user=user, permission=permission)

                    # Cập nhật vai trò người dùng
                    selected_role = request.POST.get('user_role')
                    if selected_role:
                        from permissions.models import UserRole, Role

                        # Xóa tất cả vai trò hiện tại của người dùng
                        UserRole.objects.filter(user=user).delete()

                        # Thêm vai trò mới
                        role = Role.objects.get(id=selected_role)
                        UserRole.objects.create(user=user, role=role)

                    user.save()

                    # Log user activity
                    UserActivity.objects.create(
                        user=request.user,
                        activity_type='user_update',
                        description=f'Cập nhật thông tin người dùng {user.username}',
                        ip_address=get_client_ip(request)
                    )

                    messages.success(request, f'Thông tin người dùng {user.username} đã được cập nhật thành công!')
                    return redirect('users:list')
                except Exception as e:
                    messages.error(request, f'Lỗi khi cập nhật thông tin người dùng: {str(e)}')
            else:
                # Hiển thị lỗi form cho chế độ chỉnh sửa
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f'Lỗi trong trường {field}: {error}')
                if profile_form:
                    for field, errors in profile_form.errors.items():
                        for error in errors:
                            messages.error(request, f'Lỗi trong trường hồ sơ {field}: {error}')
        else:
            # Xử lý tạo mới
            if form.is_valid() and profile_form.is_valid():
                try:
                    # Tạo user trước
                    user = form.save()

                    # Set additional fields
                    if 'is_staff' in request.POST:
                        user.is_staff = True
                    if 'is_superuser' in request.POST:
                        user.is_superuser = True
                    if 'is_active' in request.POST:
                        user.is_active = True
                    else:
                        user.is_active = False

                    user.save()
                    print(f"User created: {user}")

                    # Đợi một chút để signal hoàn thành và tạo profile
                    user.refresh_from_db()

                    # Profile đã được tạo tự động bởi signal, chỉ cần cập nhật dữ liệu
                    try:
                        profile = user.profile
                        print(f"Found existing profile: {profile}")

                        # Cập nhật dữ liệu từ form vào profile hiện có bằng cách sử dụng queryset update
                        profile_data = {}
                        for field_name, field_value in profile_form.cleaned_data.items():
                            if field_value is not None and field_value != '':  # Chỉ cập nhật nếu có giá trị
                                profile_data[field_name] = field_value

                        print(f"Profile data to update: {profile_data}")

                        # Sử dụng queryset update để tránh lỗi EmptyResultSet
                        if profile_data:
                            UserProfile.objects.filter(user=user).update(**profile_data)
                            print(f"Profile updated with data: {profile_data}")
                        else:
                            print(f"No profile data to update")

                    except UserProfile.DoesNotExist:
                        # Nếu profile chưa tồn tại (trường hợp hiếm), tạo mới
                        print("Creating new profile")
                        profile_data = {'user': user}

                        # Cập nhật dữ liệu từ form
                        for field_name, field_value in profile_form.cleaned_data.items():
                            if field_value is not None and field_value != '':
                                profile_data[field_name] = field_value

                        profile = UserProfile.objects.create(**profile_data)
                        print(f"Profile created: {profile}")

                    # Cập nhật vai trò người dùng
                    selected_role = request.POST.get('user_role')
                    if selected_role:
                        from permissions.models import UserRole, Role
                        role = Role.objects.get(id=selected_role)
                        UserRole.objects.create(user=user, role=role)
                        print(f"Role created: {role}")

                    # Cập nhật quyền người dùng
                    user_permissions = request.POST.getlist('user_permissions')
                    if user_permissions:
                        from django.contrib.auth.models import Permission
                        permissions = Permission.objects.filter(id__in=user_permissions)
                        user.user_permissions.set(permissions)

                    # Log user activity
                    UserActivity.objects.create(
                        user=request.user,
                        activity_type='user_create',
                        description=f'Tạo người dùng mới {user.username}',
                        ip_address=get_client_ip(request)
                    )

                    messages.success(request, f'Người dùng {user.username} đã được tạo thành công!')
                    return redirect('users:list')
                except Exception as e:
                    import traceback
                    error_details = traceback.format_exc()
                    print(f"Error creating user: {error_details}")  # Debug log
                    messages.error(request, f'Lỗi khi tạo người dùng: {str(e)}')
            else:
                # Debug: In ra lỗi validation
                print(f"Form valid: {form.is_valid()}")
                print(f"Profile form valid: {profile_form.is_valid()}")
                print(f"Form errors: {form.errors}")
                print(f"Profile form errors: {profile_form.errors}")

                # Hiển thị lỗi form cho chế độ tạo mới
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f'Lỗi trong trường {field}: {error}')
                if profile_form:
                    for field, errors in profile_form.errors.items():
                        for error in errors:
                            messages.error(request, f'Lỗi trong trường hồ sơ {field}: {error}')
    else:
        # GET request
        if is_edit_mode:
            form = UserEditForm(instance=user)
            profile_form = ProfileEditForm(instance=user.profile)
        else:
            form = UserRegistrationForm()
            profile_form = ProfileEditForm()

    # Import các model cần thiết
    try:
        from permissions.models import UserRole, Role, UserPermission

        if is_edit_mode:
            # Lấy danh sách vai trò của người dùng
            user_roles = UserRole.objects.filter(user=user).select_related('role')
            # Lấy danh sách quyền riêng lẻ của người dùng (custom permissions)
            user_custom_permissions = UserPermission.objects.filter(user=user).values_list('permission_id', flat=True)
        else:
            user_roles = []
            user_custom_permissions = []

        # Lấy danh sách tất cả vai trò
        available_roles = Role.objects.all().order_by('name')

        # Tạo dictionary chứa quyền của từng vai trò
        roles_permissions = {}
        for role in available_roles:
            roles_permissions[role.id] = [p.pk for p in role.custom_permissions.all()]

        # Lấy tất cả custom permissions và nhóm theo module
        permissions = CustomPermission.objects.all().order_by('module', 'code')

        # Nhóm quyền theo module
        grouped_permissions = {}
        for permission in permissions:
            module_name = permission.module

            if module_name not in grouped_permissions:
                grouped_permissions[module_name] = []

            grouped_permissions[module_name].append(permission)

    except ImportError:
        user_roles = []
        available_roles = []
        user_custom_permissions = []
        grouped_permissions = {}
        roles_permissions = {}

    # Chuẩn bị context
    context = {
        'form': form,
        'profile_form': profile_form,  # Luôn truyền profile_form (có thể là None)
        'is_edit_mode': is_edit_mode,
        'available_roles': available_roles,
        'grouped_permissions': grouped_permissions,
        'roles_permissions': roles_permissions,
        'user_roles': user_roles,
        'user_custom_permissions': user_custom_permissions,
    }

    if is_edit_mode:
        context.update({
            'user_id': user.id,
        })

    return render(request, 'users/edit.html', context)

@login_required
def change_password(request, user_id):
    user = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        form = AdminPasswordChangeForm(user, request.POST)
        if form.is_valid():
            try:
                form.save()

                # Log user activity
                UserActivity.objects.create(
                    user=request.user,
                    activity_type='password_change',
                    description=f'Thay đổi mật khẩu cho người dùng {user.username}',
                    ip_address=get_client_ip(request)
                )

                messages.success(request, f'Mật khẩu của người dùng {user.username} đã được thay đổi thành công!')
                return redirect('users:list')
            except Exception as e:
                messages.error(request, f'Lỗi khi thay đổi mật khẩu: {str(e)}')
        else:
            # Hiển thị lỗi form
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'Lỗi trong trường {field}: {error}')
    else:
        form = AdminPasswordChangeForm(user)

    return render(request, 'users/change_password.html', {
        'form': form,
        'user_id': user.id,
        'username': user.username
    })

@login_required
def profile(request):
    if request.method == 'POST':
        user_form = UserEditForm(request.POST, instance=request.user)
        profile_form = ProfileEditForm(request.POST, request.FILES, instance=request.user.profile)

        if user_form.is_valid() and profile_form.is_valid():
            user_form.save()
            profile_form.save()

            # Log user activity
            UserActivity.objects.create(
                user=request.user,
                activity_type='profile_update',
                description='Cập nhật thông tin cá nhân',
                ip_address=get_client_ip(request)
            )

            messages.success(request, 'Thông tin cá nhân của bạn đã được cập nhật thành công!')
            return redirect('users:profile')
    else:
        user_form = UserEditForm(instance=request.user)
        profile_form = ProfileEditForm(instance=request.user.profile)

    return render(request, 'users/profile.html', {
        'form': user_form,
        'profile_form': profile_form
    })

@login_required
def change_password_self(request):
    if request.method == 'POST':
        form = UserPasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            update_session_auth_hash(request, user)  # Keep user logged in

            # Log user activity
            UserActivity.objects.create(
                user=request.user,
                activity_type='password_change',
                description='Thay đổi mật khẩu',
                ip_address=get_client_ip(request)
            )

            messages.success(request, 'Mật khẩu của bạn đã được thay đổi thành công!')
            return redirect('users:profile')
    else:
        form = UserPasswordChangeForm(request.user)

    return render(request, 'users/change_password.html', {
        'form': form,
        'user_id': request.user.id,
        'username': request.user.username
    })

def password_reset(request):
    if request.method == 'POST':
        form = PasswordResetRequestForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = User.objects.get(email=email)
                # In a real application, you would send an email with a password reset link
                # For now, we'll just show a success message
                messages.success(request, 'Hướng dẫn đặt lại mật khẩu đã được gửi đến email của bạn.')
                return redirect('users:login')
            except User.DoesNotExist:
                messages.error(request, 'Không tìm thấy tài khoản với email này.')
    else:
        form = PasswordResetRequestForm()

    return render(request, 'users/password_reset.html', {'form': form})

@login_required
def export_users(request):
    """
    Export users to Excel file
    """
    # Create a workbook and add a worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Danh sách người dùng"

    # Define styles
    header_font = Font(name='Arial', bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

    # Define borders
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # Define headers
    headers = [
        'ID', 'Tên đăng nhập', 'Email', 'Họ', 'Tên', 'Nhân viên', 'Quản trị viên',
        'Hoạt động', 'Ngày tham gia', 'Đăng nhập cuối', 'Vai trò'
    ]

    # Write headers
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_num)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border

    # Get all users
    users = User.objects.all().order_by('id')

    # Write data
    for row_num, user in enumerate(users, 2):
        # Get user role
        try:
            from permissions.models import UserRole
            user_role = UserRole.objects.filter(user=user).first()
            role_name = user_role.role.name if user_role else ""
        except:
            role_name = ""

        # Get last login
        last_login = user.last_login.strftime("%d/%m/%Y %H:%M:%S") if user.last_login else ""

        # Write user data
        row = [
            user.id,
            user.username,
            user.email,
            user.last_name,
            user.first_name,
            'Có' if user.is_staff else 'Không',
            'Có' if user.is_superuser else 'Không',
            'Có' if user.is_active else 'Không',
            user.date_joined.strftime("%d/%m/%Y %H:%M:%S"),
            last_login,
            role_name
        ]

        for col_num, cell_value in enumerate(row, 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.value = cell_value
            cell.border = thin_border
            if col_num in [6, 7, 8]:  # Boolean columns
                cell.alignment = Alignment(horizontal='center')

    # Adjust column widths
    column_widths = [5, 15, 25, 15, 15, 10, 15, 10, 20, 20, 20]
    for i, width in enumerate(column_widths):
        ws.column_dimensions[get_column_letter(i+1)].width = width

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename=danh_sach_nguoi_dung_{}.xlsx'.format(
        datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    )

    # Log user activity
    UserActivity.objects.create(
        user=request.user,
        activity_type='user_export',
        description=f'Xuất danh sách người dùng',
        ip_address=get_client_ip(request)
    )

    # Save workbook to response
    wb.save(response)
    return response

@login_required
def import_users_api(request):
    if request.method == "POST":
        try:
            data = json.loads(request.body).get("data", [])
            for row in data:
                ma_bhxh = row.get("MA_BHXH", "").strip()

                profile = UserProfile.objects.filter(ma_bhxh=ma_bhxh).select_related('user').first()

                if profile:
                    user = profile.user
                    created = False
                else:
                    full_name = row.get("HO_TEN", "").strip()
                    username = generate_username_from_name(full_name)

                    user, created = User.objects.get_or_create(username=username, defaults={
                        "first_name": full_name,
                        "is_active": True,
                    })

                    if created:
                        user.set_password("123456")
                        user.save()

                department_name = row.get("TEN_KHOA", "").strip()
                department = Department.objects.filter(name__iexact=department_name).first()

                UserProfile.objects.update_or_create(user=user, defaults={
                    "ma_loai_kcb": row.get("MA_LOAI_KCB"),
                    "ma_bhxh": ma_bhxh,
                    "ma_khoa": row.get("MA_KHOA"),
                    "department": department,
                    "gioi_tinh": int(row.get("GIOI_TINH") or 0),
                    "chuc_danh_nn": int(row.get("CHUCDANH_NN") or 0),
                    "vi_tri": int(row.get("VI_TRI") or 0),
                    "practice_certificate": row.get("MACCHN"),
                    "ngay_cap_cchn": parse_excel_short_date(row.get("NGAYCAP_CCHN")),
                    "noi_cap_cchn": row.get("NOICAP_CCHN"),
                    "pham_vi_cm": row.get("PHAMVI_CM"),
                    "pham_vi_cm_bs": row.get("PHAMVI_CMBS"),
                    "dvkt_khac": row.get("DVKT_KHAC"),
                    "vb_phan_cong": row.get("VB_PHANCONG"),
                    "thoi_gian_dk": int(row.get("THOIGIAN_DK") or 0),
                    "thoi_gian_ngay": row.get("THOIGIAN_NGAY"),
                    "thoi_gian_tuan": row.get("THOIGIAN_TUAN"),
                    "cskcb_khac": row.get("CSKCB_KHAC"),
                    "cskcb_cgkt": row.get("CSKCB_CGKT"),
                    "qd_cgkt": row.get("QD_CGKT"),
                    "tu_ngay": row.get("TU_NGAY"),
                    "den_ngay": row.get("DEN_NGAY"),
                })
            return JsonResponse({"success": True})
        except Exception as e:
            return JsonResponse({"success": False, "message": str(e)})
    return JsonResponse({"success": False, "message": "Invalid method"}, status=405)

@login_required
def is_active_toogle_api(request, pk):
    """API endpoint for toggling the 'is_active' status of a user"""
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        user = get_object_or_404(User, pk=pk)
        user.is_active = not user.is_active
        user.save()
        return JsonResponse({'status': 'success', 'is_active': user.is_active})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def delete_user_api(request, pk):
    """API endpoint for deleting a user"""
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        user = get_object_or_404(User, pk=pk)
        user.delete()
        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def update_user_api(request, pk):
    '''API thực hiện việc cập nhật dữ liệu từng dòng trên tabulator'''
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        data = json.loads(request.body).get("data", [])
        for row in data:
            user_id = row.get("ID")
            user = get_object_or_404(User, id=user_id)
            user.first_name = row.get("HO_TEN") or row.get("HO_TEN")  # hoặc sinh username từ họ tên
            user.save()
        return JsonResponse({'status': 'success'})       
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})


@login_required
def export_users(request):
    """Export danh sách người dùng ra Excel"""
    export_type = request.GET.get('type', 'all')  # 'all' hoặc 'cchn'

    # Tạo queryset cơ bản
    queryset = User.objects.select_related('profile', 'profile__department').all()

    # Lọc theo loại export
    if export_type == 'cchn':
        # Chỉ lấy những user có practice_certificate (CCHN)
        queryset = queryset.filter(profile__practice_certificate__isnull=False).exclude(profile__practice_certificate='')

    # Tạo workbook và worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Danh sách nhân viên"

    # Định nghĩa header theo yêu cầu
    headers = [
        'STT', 'MA_LOAI_KCB', 'MA_KHOA', 'TEN_KHOA', 'MA_BHXH', 'HO_TEN',
        'GIOI_TINH', 'CHUCDANH_NN', 'VI_TRI', 'MACCHN', 'NGAYCAP_CCHN',
        'NOICAP_CCHN', 'PHAMVI_CM', 'PHAMVI_CMBS', 'DVKT_KHAC', 'VB_PHANCONG',
        'THOIGIAN_DK', 'THOIGIAN_NGAY', 'THOIGIAN_TUAN', 'CSKCB_KHAC',
        'CSKCB_CGKT', 'QD_CGKT', 'TUNGAY'
    ]

    # Thêm header vào worksheet
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_num, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        cell.alignment = Alignment(horizontal="center")

    # Thêm dữ liệu
    row_num = 2
    for index, user in enumerate(queryset, 1):
        profile = user.profile if hasattr(user, 'profile') else None

        # Chỉ xuất những user có profile
        if not profile:
            continue

        # Nếu export type là 'cchn', kiểm tra lại practice_certificate
        if export_type == 'cchn' and not profile.practice_certificate:
            continue

        data_row = [
            index,  # STT
            profile.ma_loai_kcb if profile.ma_loai_kcb else '',  # MA_LOAI_KCB
            profile.ma_khoa if profile.ma_khoa else '',  # MA_KHOA
            profile.department.name if profile.department else '',  # TEN_KHOA
            profile.ma_bhxh if profile.ma_bhxh else '',  # MA_BHXH
            user.first_name if user.first_name else '',  # HO_TEN
            profile.gioi_tinh if profile.gioi_tinh else '',  # GIOI_TINH
            profile.chuc_danh_nn if profile.chuc_danh_nn else '',  # CHUCDANH_NN
            profile.vi_tri if profile.vi_tri else '',  # VI_TRI
            profile.practice_certificate if profile.practice_certificate else '',  # MACCHN
            profile.ngay_cap_cchn if profile.ngay_cap_cchn else '',  # NGAYCAP_CCHN
            profile.noi_cap_cchn if profile.noi_cap_cchn else '',  # NOICAP_CCHN
            profile.pham_vi_cm if profile.pham_vi_cm else '',  # PHAMVI_CM
            profile.pham_vi_cm_bs if profile.pham_vi_cm_bs else '',  # PHAMVI_CMBS
            profile.dvkt_khac if profile.dvkt_khac else '',  # DVKT_KHAC
            profile.vb_phan_cong if profile.vb_phan_cong else '',  # VB_PHANCONG
            profile.thoi_gian_dk if profile.thoi_gian_dk else '',  # THOIGIAN_DK
            profile.thoi_gian_ngay if profile.thoi_gian_ngay else '',  # THOIGIAN_NGAY
            profile.thoi_gian_tuan if profile.thoi_gian_tuan else '',  # THOIGIAN_TUAN
            profile.cskcb_khac if profile.cskcb_khac else '',  # CSKCB_KHAC
            profile.cskcb_cgkt if profile.cskcb_cgkt else '',  # CSKCB_CGKT
            profile.qd_cgkt if profile.qd_cgkt else '',  # QD_CGKT
            profile.tu_ngay if profile.tu_ngay else '',  # TUNGAY
        ]

        # Thêm dữ liệu vào worksheet
        for col_num, value in enumerate(data_row, 1):
            ws.cell(row=row_num, column=col_num, value=value)

        row_num += 1

    # Tự động điều chỉnh độ rộng cột
    for column in ws.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Tạo response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

    # Tên file theo loại export
    filename = f"danh_sach_nhan_vien_{'cchn' if export_type == 'cchn' else 'tat_ca'}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    # Lưu workbook vào response
    wb.save(response)

    return response