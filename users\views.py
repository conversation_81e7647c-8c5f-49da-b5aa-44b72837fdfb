from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout, update_session_auth_hash
from django.contrib.auth.models import User
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.http import HttpResponseRedirect, HttpResponse, JsonResponse
from django.urls import reverse
from django.views.decorators.csrf import csrf_exempt
from django.utils.dateparse import parse_date
from django.db.models import Q

from departments.models import Department

from .forms import (
    UserLoginForm, UserRegistrationForm, UserEditForm, ProfileEditForm,
    UserPasswordChangeForm, AdminPasswordChangeForm, PasswordResetRequestForm,
    UserImportForm
)

from .models import UserActivity, UserProfile
from utils.ip_utils import get_client_ip
from permissions.models import Permission as CustomPermission
import openpyxl
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import datetime
import os
import uuid
import json
from .services import parse_excel_short_date, generate_username_from_name, parse_to_short_date

def login_view(request):
    if request.user.is_authenticated:
        return redirect('home')

    if request.method == 'POST':
        form = UserLoginForm(request.POST)
        if form.is_valid():
            username = form.cleaned_data['username']
            password = form.cleaned_data['password']
            remember = form.cleaned_data['remember']

            user = authenticate(request, username=username, password=password)

            if user is not None:
                login(request, user)

                # Log user activity
                UserActivity.objects.create(
                    user=user,
                    activity_type='login',
                    description='Đăng nhập thành công',
                    ip_address=get_client_ip(request)
                )

                # Set session expiry based on remember checkbox
                if not remember:
                    request.session.set_expiry(0)  # Session expires when browser closes

                next_url = request.GET.get('next', 'home')
                return redirect(next_url)
            else:
                form.add_error(None, 'Tên đăng nhập hoặc mật khẩu không đúng.')
    else:
        form = UserLoginForm()

    return render(request, 'users/login.html', {'form': form})

def logout_view(request):
    if request.user.is_authenticated:
        # Log user activity
        UserActivity.objects.create(
            user=request.user,
            activity_type='logout',
            description='Đăng xuất thành công',
            ip_address=get_client_ip(request)
        )

        logout(request)

    return redirect('users:login')

@login_required
def user_list(request):
    return render(request, 'users/list.html')

@login_required
def user_list_api(request):
    users = User.objects.select_related('profile__department', 'profile__position').all()

    data = []
    for user in users:
        profile = getattr(user, 'profile', None)

        row = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "is_active": user.is_active,
            "is_staff": user.is_staff,
            "is_superuser": user.is_superuser,
        }

        # Gộp các trường profile (không lồng)
        if profile:
            row.update({
                "ma_khoa": profile.ma_khoa,
                "gioi_tinh": profile.gioi_tinh,
                "ma_loai_kcb": profile.ma_loai_kcb,
                "chuc_danh_nn": profile.chuc_danh_nn,
                "vi_tri": profile.vi_tri,
                "practice_certificate": profile.practice_certificate,
                "ngay_cap_cchn": parse_to_short_date(profile.ngay_cap_cchn),
                "noi_cap_cchn": profile.noi_cap_cchn,
                "pham_vi_cm": profile.pham_vi_cm,
                "pham_vi_cm_bs": profile.pham_vi_cm_bs,
                "dvkt_khac": profile.dvkt_khac,
                "vb_phan_cong": profile.vb_phan_cong,
                "thoi_gian_dk": profile.thoi_gian_dk,
                "thoi_gian_ngay": profile.thoi_gian_ngay,
                "thoi_gian_tuan": profile.thoi_gian_tuan,
                "cskcb_khac": profile.cskcb_khac,
                "cskcb_cgkt": profile.cskcb_cgkt,
                "qd_cgkt": profile.qd_cgkt,
                "tu_ngay": parse_to_short_date(profile.tu_ngay),
                "den_ngay": parse_to_short_date(profile.den_ngay),
                "department": profile.department.name if profile.department else "",
                "position": profile.position.name if profile.position else "",
            })

        data.append(row)

    return JsonResponse(data, safe=False)

@login_required
def user_detail(request, user_id):
    user = get_object_or_404(User, id=user_id)
    return render(request, 'users/detail.html', {'user': user})

@login_required
def user_create(request):
    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            try:
                user = form.save()

                # Set additional fields
                if 'is_staff' in request.POST:
                    user.is_staff = True
                if 'is_superuser' in request.POST:
                    user.is_superuser = True
                if 'is_active' in request.POST:
                    user.is_active = True
                else:
                    user.is_active = False

                # Tạo profile cho người dùng
                from .models import UserProfile
                UserProfile.objects.create(user=user)

                # Cập nhật vai trò người dùng
                selected_role = request.POST.get('user_role')
                if selected_role:
                    from permissions.models import UserRole, Role
                    role = Role.objects.get(id=selected_role)
                    UserRole.objects.create(user=user, role=role)

                # Cập nhật quyền người dùng
                user_permissions = request.POST.getlist('user_permissions')
                if user_permissions:
                    from django.contrib.auth.models import Permission
                    permissions = Permission.objects.filter(id__in=user_permissions)
                    user.user_permissions.set(permissions)

                user.save()

                # Log user activity
                UserActivity.objects.create(
                    user=request.user,
                    activity_type='user_create',
                    description=f'Tạo người dùng mới {user.username}',
                    ip_address=get_client_ip(request)
                )

                messages.success(request, f'Người dùng {user.username} đã được tạo thành công!')
                return redirect('users:list')
            except Exception as e:
                messages.error(request, f'Lỗi khi tạo người dùng: {str(e)}')
        else:
            # Hiển thị lỗi form
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'Lỗi trong trường {field}: {error}')
    else:
        form = UserRegistrationForm()

    # Import các model cần thiết
    try:
        from permissions.models import Role

        # Lấy danh sách tất cả vai trò
        available_roles = Role.objects.all().order_by('name')

        # Tạo dictionary chứa quyền của từng vai trò
        roles_permissions = {}
        for role in available_roles:
            roles_permissions[role.id] = [p.pk for p in role.custom_permissions.all()]

        # Lấy tất cả custom permissions và nhóm theo module
        permissions = CustomPermission.objects.all().order_by('module', 'code')

        # Nhóm quyền theo module
        grouped_permissions = {}
        for permission in permissions:
            module_name = permission.module

            if module_name not in grouped_permissions:
                grouped_permissions[module_name] = []

            grouped_permissions[module_name].append(permission)



    except ImportError:
        available_roles = []
        grouped_permissions = {}
        roles_permissions = {}

    return render(request, 'users/create.html', {
        'form': form,
        'available_roles': available_roles,
        'grouped_permissions': grouped_permissions,
        'roles_permissions': roles_permissions
    })

@login_required
def user_edit(request, user_id):
    user = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        form = UserEditForm(request.POST, instance=user)
        profile_form = ProfileEditForm(request.POST, request.FILES, instance=user.profile)

        if form.is_valid() and profile_form.is_valid():
            try:
                form.save()
                profile_form.save()

                # Set additional fields
                user.is_staff = 'is_staff' in request.POST
                user.is_superuser = 'is_superuser' in request.POST
                user.is_active = 'is_active' in request.POST

                # Cập nhật quyền riêng lẻ của người dùng (custom permissions)
                custom_permissions = request.POST.getlist('custom_permissions')
                from permissions.models import UserPermission

                # Xóa tất cả quyền riêng lẻ hiện tại
                UserPermission.objects.filter(user=user).delete()

                # Thêm quyền riêng lẻ mới
                if custom_permissions:
                    permissions = CustomPermission.objects.filter(id__in=custom_permissions)
                    for permission in permissions:
                        UserPermission.objects.create(user=user, permission=permission)

                # Cập nhật vai trò người dùng
                selected_role = request.POST.get('user_role')
                if selected_role:
                    from permissions.models import UserRole, Role

                    # Xóa tất cả vai trò hiện tại của người dùng
                    UserRole.objects.filter(user=user).delete()

                    # Thêm vai trò mới
                    role = Role.objects.get(id=selected_role)
                    UserRole.objects.create(user=user, role=role)

                user.save()

                # Log user activity
                UserActivity.objects.create(
                    user=request.user,
                    activity_type='user_update',
                    description=f'Cập nhật thông tin người dùng {user.username}',
                    ip_address=get_client_ip(request)
                )

                messages.success(request, f'Thông tin người dùng {user.username} đã được cập nhật thành công!')

                # Chuyển hướng đến trang danh sách người dùng
                return redirect('users:list')
            except Exception as e:
                messages.error(request, f'Lỗi khi cập nhật thông tin người dùng: {str(e)}')
        else:
            # Hiển thị lỗi form
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'Lỗi trong trường {field}: {error}')
            for field, errors in profile_form.errors.items():
                for error in errors:
                    messages.error(request, f'Lỗi trong trường hồ sơ {field}: {error}')
    else:
        form = UserEditForm(instance=user)
        profile_form = ProfileEditForm(instance=user.profile)

    # Import các model cần thiết
    try:
        from permissions.models import UserRole, Role, UserPermission

        # Lấy danh sách vai trò của người dùng
        user_roles = UserRole.objects.filter(user=user).select_related('role')

        # Lấy danh sách tất cả vai trò
        available_roles = Role.objects.all().order_by('name')

        # Tạo dictionary chứa quyền của từng vai trò
        roles_permissions = {}
        for role in available_roles:
            roles_permissions[role.id] = [p.pk for p in role.custom_permissions.all()]

        # Lấy danh sách quyền riêng lẻ của người dùng (custom permissions)
        user_custom_permissions = UserPermission.objects.filter(user=user).values_list('permission_id', flat=True)

        # Lấy tất cả custom permissions và nhóm theo module
        permissions = CustomPermission.objects.all().order_by('module', 'code')

        # Nhóm quyền theo module
        grouped_permissions = {}
        for permission in permissions:
            module_name = permission.module

            if module_name not in grouped_permissions:
                grouped_permissions[module_name] = []

            grouped_permissions[module_name].append(permission)

    except ImportError:
        user_roles = []
        available_roles = []
        user_permissions = []
        grouped_permissions = {}
        roles_permissions = {}

    return render(request, 'users/edit.html', {
        'form': form,
        'profile_form': profile_form,
        'user_id': user.id,
        'user_roles': user_roles,
        'available_roles': available_roles,
        'user_custom_permissions': user_custom_permissions,
        'grouped_permissions': grouped_permissions,
        'roles_permissions': roles_permissions
    })

@login_required
def change_password(request, user_id):
    user = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        form = AdminPasswordChangeForm(user, request.POST)
        if form.is_valid():
            try:
                form.save()

                # Log user activity
                UserActivity.objects.create(
                    user=request.user,
                    activity_type='password_change',
                    description=f'Thay đổi mật khẩu cho người dùng {user.username}',
                    ip_address=get_client_ip(request)
                )

                messages.success(request, f'Mật khẩu của người dùng {user.username} đã được thay đổi thành công!')
                return redirect('users:list')
            except Exception as e:
                messages.error(request, f'Lỗi khi thay đổi mật khẩu: {str(e)}')
        else:
            # Hiển thị lỗi form
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'Lỗi trong trường {field}: {error}')
    else:
        form = AdminPasswordChangeForm(user)

    return render(request, 'users/change_password.html', {
        'form': form,
        'user_id': user.id,
        'username': user.username
    })

@login_required
def profile(request):
    if request.method == 'POST':
        user_form = UserEditForm(request.POST, instance=request.user)
        profile_form = ProfileEditForm(request.POST, request.FILES, instance=request.user.profile)

        if user_form.is_valid() and profile_form.is_valid():
            user_form.save()
            profile_form.save()

            # Log user activity
            UserActivity.objects.create(
                user=request.user,
                activity_type='profile_update',
                description='Cập nhật thông tin cá nhân',
                ip_address=get_client_ip(request)
            )

            messages.success(request, 'Thông tin cá nhân của bạn đã được cập nhật thành công!')
            return redirect('users:profile')
    else:
        user_form = UserEditForm(instance=request.user)
        profile_form = ProfileEditForm(instance=request.user.profile)

    return render(request, 'users/profile.html', {
        'form': user_form,
        'profile_form': profile_form
    })

@login_required
def change_password_self(request):
    if request.method == 'POST':
        form = UserPasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            update_session_auth_hash(request, user)  # Keep user logged in

            # Log user activity
            UserActivity.objects.create(
                user=request.user,
                activity_type='password_change',
                description='Thay đổi mật khẩu',
                ip_address=get_client_ip(request)
            )

            messages.success(request, 'Mật khẩu của bạn đã được thay đổi thành công!')
            return redirect('users:profile')
    else:
        form = UserPasswordChangeForm(request.user)

    return render(request, 'users/change_password.html', {
        'form': form,
        'user_id': request.user.id,
        'username': request.user.username
    })

def password_reset(request):
    if request.method == 'POST':
        form = PasswordResetRequestForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = User.objects.get(email=email)
                # In a real application, you would send an email with a password reset link
                # For now, we'll just show a success message
                messages.success(request, 'Hướng dẫn đặt lại mật khẩu đã được gửi đến email của bạn.')
                return redirect('users:login')
            except User.DoesNotExist:
                messages.error(request, 'Không tìm thấy tài khoản với email này.')
    else:
        form = PasswordResetRequestForm()

    return render(request, 'users/password_reset.html', {'form': form})

@login_required
def export_users(request):
    """
    Export users to Excel file
    """
    # Create a workbook and add a worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Danh sách người dùng"

    # Define styles
    header_font = Font(name='Arial', bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

    # Define borders
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # Define headers
    headers = [
        'ID', 'Tên đăng nhập', 'Email', 'Họ', 'Tên', 'Nhân viên', 'Quản trị viên',
        'Hoạt động', 'Ngày tham gia', 'Đăng nhập cuối', 'Vai trò'
    ]

    # Write headers
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_num)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border

    # Get all users
    users = User.objects.all().order_by('id')

    # Write data
    for row_num, user in enumerate(users, 2):
        # Get user role
        try:
            from permissions.models import UserRole
            user_role = UserRole.objects.filter(user=user).first()
            role_name = user_role.role.name if user_role else ""
        except:
            role_name = ""

        # Get last login
        last_login = user.last_login.strftime("%d/%m/%Y %H:%M:%S") if user.last_login else ""

        # Write user data
        row = [
            user.id,
            user.username,
            user.email,
            user.last_name,
            user.first_name,
            'Có' if user.is_staff else 'Không',
            'Có' if user.is_superuser else 'Không',
            'Có' if user.is_active else 'Không',
            user.date_joined.strftime("%d/%m/%Y %H:%M:%S"),
            last_login,
            role_name
        ]

        for col_num, cell_value in enumerate(row, 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.value = cell_value
            cell.border = thin_border
            if col_num in [6, 7, 8]:  # Boolean columns
                cell.alignment = Alignment(horizontal='center')

    # Adjust column widths
    column_widths = [5, 15, 25, 15, 15, 10, 15, 10, 20, 20, 20]
    for i, width in enumerate(column_widths):
        ws.column_dimensions[get_column_letter(i+1)].width = width

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename=danh_sach_nguoi_dung_{}.xlsx'.format(
        datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    )

    # Log user activity
    UserActivity.objects.create(
        user=request.user,
        activity_type='user_export',
        description=f'Xuất danh sách người dùng',
        ip_address=get_client_ip(request)
    )

    # Save workbook to response
    wb.save(response)
    return response

@login_required
def import_users_api(request):
    if request.method == "POST":
        try:
            data = json.loads(request.body).get("data", [])
            for row in data:
                ma_bhxh = row.get("MA_BHXH", "").strip()

                profile = UserProfile.objects.filter(ma_bhxh=ma_bhxh).select_related('user').first()

                if profile:
                    user = profile.user
                    created = False
                else:
                    full_name = row.get("HO_TEN", "").strip()
                    username = generate_username_from_name(full_name)

                    user, created = User.objects.get_or_create(username=username, defaults={
                        "first_name": full_name,
                        "is_active": True,
                    })

                    if created:
                        user.set_password("123456")
                        user.save()

                department_name = row.get("TEN_KHOA", "").strip()
                department = Department.objects.filter(name__iexact=department_name).first()

                UserProfile.objects.update_or_create(user=user, defaults={
                    "ma_loai_kcb": row.get("MA_LOAI_KCB"),
                    "ma_bhxh": ma_bhxh,
                    "ma_khoa": row.get("MA_KHOA"),
                    "department": department,
                    "gioi_tinh": int(row.get("GIOI_TINH") or 0),
                    "chuc_danh_nn": int(row.get("CHUCDANH_NN") or 0),
                    "vi_tri": int(row.get("VI_TRI") or 0),
                    "practice_certificate": row.get("MACCHN"),
                    "ngay_cap_cchn": parse_excel_short_date(row.get("NGAYCAP_CCHN")),
                    "noi_cap_cchn": row.get("NOICAP_CCHN"),
                    "pham_vi_cm": row.get("PHAMVI_CM"),
                    "pham_vi_cm_bs": row.get("PHAMVI_CMBS"),
                    "dvkt_khac": row.get("DVKT_KHAC"),
                    "vb_phan_cong": row.get("VB_PHANCONG"),
                    "thoi_gian_dk": int(row.get("THOIGIAN_DK") or 0),
                    "thoi_gian_ngay": row.get("THOIGIAN_NGAY"),
                    "thoi_gian_tuan": row.get("THOIGIAN_TUAN"),
                    "cskcb_khac": row.get("CSKCB_KHAC"),
                    "cskcb_cgkt": row.get("CSKCB_CGKT"),
                    "qd_cgkt": row.get("QD_CGKT"),
                    "tu_ngay": row.get("TU_NGAY"),
                    "den_ngay": row.get("DEN_NGAY"),
                })
            return JsonResponse({"success": True})
        except Exception as e:
            return JsonResponse({"success": False, "message": str(e)})
    return JsonResponse({"success": False, "message": "Invalid method"}, status=405)

@login_required
def is_active_toogle_api(request, pk):
    """API endpoint for toggling the 'is_active' status of a user"""
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        user = get_object_or_404(User, pk=pk)
        user.is_active = not user.is_active
        user.save()
        return JsonResponse({'status': 'success', 'is_active': user.is_active})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def delete_user_api(request, pk):
    """API endpoint for deleting a user"""
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        user = get_object_or_404(User, pk=pk)
        user.delete()
        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def update_user_api(request, pk):
    '''API thực hiện việc cập nhật dữ liệu từng dòng trên tabulator'''
    if request.method != 'POST':
        return HttpResponse("Phương thức không hợp lệ", status=405)
    try:
        data = json.loads(request.body).get("data", [])
        for row in data:
            user_id = row.get("ID")
            user = get_object_or_404(User, id=user_id)
            user.first_name = row.get("HO_TEN") or row.get("HO_TEN")  # hoặc sinh username từ họ tên
            user.save()
        return JsonResponse({'status': 'success'})       
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})