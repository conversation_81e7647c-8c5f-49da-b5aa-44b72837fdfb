from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver
from departments.models import Department, Position

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, blank=True, null=True, related_name='user_profiles')
    position = models.ForeignKey(Position, on_delete=models.SET_NULL, blank=True, null=True, related_name='user_profiles')
    practice_certificate = models.CharField(max_length=250, blank=True, null=True, verbose_name='Chứng chỉ hành nghề')
    profile_image = models.ImageField(upload_to='profile_images/', blank=True, null=True)
    last_activity = models.DateTimeField(blank=True, null=True)
    ma_bhxh = models.Cha<PERSON><PERSON><PERSON>(max_length=20, blank=True, null=True, verbose_name='Mã BHXH')
    ma_loai_kcb = models.CharField(max_length=20, blank=True, null=True, verbose_name='Mã loại khám chữa bệnh')
    ma_khoa = models.CharField(max_length=100, blank=True, null=True, verbose_name='Mã khoa BHXH')
    gioi_tinh = models.IntegerField(blank=True, null=True, verbose_name='Giới tính')
    chuc_danh_nn = models.IntegerField(blank=True, null=True, verbose_name='Chức danh nghề nghiệp')
    vi_tri = models.IntegerField(blank=True, null=True, verbose_name='Vị trí công tác')
    ngay_cap_cchn = models.CharField(max_length=8, blank=True, null=True, verbose_name='Ngày cấp Chứng chỉ hành nghề')
    noi_cap_cchn = models.CharField(max_length=250, blank=True, null=True, verbose_name='Nơi cấp Chứng chỉ hành nghề')
    pham_vi_cm = models.CharField(max_length=250, blank=True, null=True, verbose_name='Phạm vi chuyên môn')
    pham_vi_cm_bs = models.CharField(max_length=250, blank=True, null=True, verbose_name='Phạm vi chuyên môn bác sĩ')
    dvkt_khac = models.CharField(max_length=250, blank=True, null=True, verbose_name='Dịch vụ kĩ thuật khác')
    vb_phan_cong = models.CharField(max_length=250, blank=True, null=True, verbose_name='Văn bản phân công')
    thoi_gian_dk = models.IntegerField(blank=True, null=True, verbose_name='Thời gian đăng ký')
    thoi_gian_ngay = models.CharField(max_length=250, blank=True, null=True, verbose_name='Thời gian ngày')
    thoi_gian_tuan = models.CharField(max_length=250, blank=True, null=True, verbose_name='Thời gian tuần')
    cskcb_khac = models.CharField(max_length=250, blank=True, null=True, verbose_name='Cơ sở khám chữa bệnh khác')
    cskcb_cgkt = models.CharField(max_length=250, blank=True, null=True, verbose_name='Cơ sở khám chữa bệnh chuyển giao kĩ thuật')
    qd_cgkt = models.CharField(max_length=50, blank=True, null=True, verbose_name='Quyết định chuyển giao kĩ thuật')
    tu_ngay = models.CharField(max_length=8, blank=True, null=True, verbose_name='Ngày bắt đầu làm việc của người hành nghề tại cơ sở KCB')
    den_ngay = models.CharField(max_length=8, blank=True, null=True, verbose_name='Ngày kết thúc làm việc của người hành nghề tại cơ sở KCB')

    def __str__(self):
        return f"{self.user.username}'s Profile"

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    if hasattr(instance, 'profile'):
        instance.profile.save()

class UserActivity(models.Model):
    ACTIVITY_TYPES = (
        ('login', 'Đăng nhập'),
        ('logout', 'Đăng xuất'),
        ('profile_update', 'Cập nhật hồ sơ'),
        ('password_change', 'Đổi mật khẩu'),
        ('other', 'Khác'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities')
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES)
    description = models.TextField(blank=True, null=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'Hoạt động người dùng'
        verbose_name_plural = 'Hoạt động người dùng'

    def __str__(self):
        return f"{self.user.username} - {self.get_activity_type_display()} - {self.timestamp}"