from django import forms
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm, PasswordChangeForm, SetPasswordForm
from .models import UserProfile
from departments.models import Department, Position
from .services import parse_to_short_date, parse_to_yyyyMMdd
from danhmuckhac.models import PhamViChuyenMon

class UserLoginForm(forms.Form):
    username = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Tên đăng nhập'})
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Mật khẩu'})
    )
    remember = forms.BooleanField(required=False)

class UserRegistrationForm(UserCreationForm):
    email = forms.EmailField(required=False)
    first_name = forms.Char<PERSON>ield(required=True)
    last_name = forms.Char<PERSON>ield(required=False)

    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'password1', 'password2')

    def save(self, commit=True):
        user = super(UserRegistrationForm, self).save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']

        if commit:
            user.save()

        return user

class UserEditForm(forms.ModelForm):
    email = forms.EmailField(required=False)
    first_name = forms.CharField(required=True)
    last_name = forms.CharField(required=False)
    is_active = forms.BooleanField(required=False)
    is_staff = forms.BooleanField(required=False)
    is_superuser = forms.BooleanField(required=False)

    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'is_active', 'is_staff', 'is_superuser')

class ProfileEditForm(forms.ModelForm):
    tu_ngay = forms.CharField(
        required=False,
        max_length=10,  # <-- override max_length lớn hơn
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'dd/MM/yyyy'})
    )
    den_ngay = forms.CharField(
        required=False,
        max_length=20,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'dd/MM/yyyy'})
    )
    ngay_cap_cchn = forms.CharField(
        required=False,
        max_length=20,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'dd/MM/yyyy'})
    )
    pham_vi_cm = forms.ModelMultipleChoiceField(
        queryset=PhamViChuyenMon.objects.all().order_by("ma_pham_vi"),
        required=False,
        widget=forms.SelectMultiple(attrs={'class': 'form-control select2', 'multiple': 'multiple'}),
        to_field_name="ma_pham_vi"
    )
    GENDER_CHOICES = [
        (1, '1.Nam'),
        (2, '2.Nữ'),
    ]

    # Mã hóa loại khám bệnh, chữa bệnh (1: khám bệnh, chữa bệnh ngoại trú; 4: điều trị nội trú; 2:  Bệnh viện chuyên khoa được cấp có thẩm quyền phê duyệt thành lập liên khoa gồm Khoa khám bệnh và các khoa khác theo quy định của Bộ Y tế)
    MA_LOAI_KCB = [
        (0, '---------'),
        (1, '1. Khám bệnh, chữa bệnh ngoại trú'),
        (2, '2. Bệnh viện chuyên khoa được cấp có thẩm quyền phê duyệt thành lập liên khoa gồm Khoa khám bệnh và các khoa khác theo quy định của Bộ Y tế'),
        (4, '4. Điều trị nội trú'),
    ]

    # Mã hóa vị trí (1: người chịu trách nhiệm chuyên môn; 2: Trưởng khoa; 3: người chịu trách nhiệm chuyên môn kiêm Trưởng khoa)
    VI_TRI = [
        (0, '----------'),
        (1, '1. Người chịu trách nhiệm chuyên môn'),
        (2, '2. Trưởng khoa'),
        (3, '3. người chịu trách nhiệm chuyên môn kiêm Trưởng khoa'),
    ]

    # 'Mã hóa chức danh nghề nghiệp (1: Bác sĩ; 2: Y sĩ; 3: Điều dưỡng; 4: Hộ sinh; 5: Kỹ thuật viên; 6: Cử nhân X-quang; 7: Dược sĩ đại học; 8: Dược sĩ trung cấp; 9: Lương y; 10: cử nhân xét nghiệm)
    CDNN = [
        (0, '--------'),
        (1, '1. Bác sĩ'),
        (2, '2. Y sĩ'),
        (3, '3. Điều dưỡng'),
        (4, '4. Hộ sinh'),
        (5, '5. Kỹ thuật viên'),
        (6, '6. Cử nhân X-quang'),
        (7, '7. Dược sĩ đại học'),
        (8, '8. Dược sĩ trung cấp'),
        (9, '9. Lương y'),
        (10, '10. cử nhân xét nghiệm'),
    ]

    # Mã hóa thời gian đăng ký hành nghề (1: toàn thời gian; 2: bán thời gian)
    THOI_GIAN_DK = [
        (1, '1. Toàn thời gian'),
        (2, '2. Bán thời gian'),
    ]

    gioi_tinh = forms.ChoiceField(choices=GENDER_CHOICES, widget=forms.Select(attrs={'class': 'form-control'}))
    ma_loai_kcb = forms.ChoiceField(choices=MA_LOAI_KCB, widget=forms.Select(attrs={'class': 'form-control'}))
    vi_tri = forms.ChoiceField(choices=VI_TRI, widget=forms.Select(attrs={'class': 'form-control'}))
    chuc_danh_nn = forms.ChoiceField(choices=CDNN, widget=forms.Select(attrs={'class': 'form-control'}))
    thoi_gian_dk = forms.ChoiceField(choices=THOI_GIAN_DK, widget=forms.Select(attrs={'class': 'form-control'}))
    class Meta:
        model = UserProfile
        fields = ('phone_number', 'department', 'position', 'practice_certificate', 'ma_bhxh','profile_image', 'ma_loai_kcb', 'ma_khoa', 'gioi_tinh', 'chuc_danh_nn', 
                  'vi_tri', 'ngay_cap_cchn', 'noi_cap_cchn', 'pham_vi_cm', 'pham_vi_cm_bs', 'dvkt_khac', 'vb_phan_cong', 'thoi_gian_dk', 'thoi_gian_ngay', 
                  'thoi_gian_tuan', 'cskcb_khac', 'cskcb_cgkt','qd_cgkt','tu_ngay', 'den_ngay')
        widgets = {
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
            'department': forms.Select(attrs={'class': 'form-control select2'}),
            'position': forms.Select(attrs={'class': 'form-control select2'}),
            'practice_certificate': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Nhập mã chứng chỉ hành nghề'}),
            'profile_image': forms.FileInput(attrs={'class': 'form-control'}),
            'ma_bhxh': forms.TextInput(attrs={'class': 'form-control'}),
            'ma_khoa': forms.TextInput(attrs={'class': 'form-control'}),
            'noi_cap_cchn': forms.TextInput(attrs={'class': 'form-control'}),
            # 'pham_vi_cm': forms.TextInput(attrs={'class': 'form-control'}),
            # 'pham_vi_cm': forms.ModelMultipleChoiceField(attrs={'class': 'form-control select2-search select2-search--inline', 'multiple': 'true'}),
            'pham_vi_cm_bs': forms.TextInput(attrs={'class': 'form-control'}),
            'dvkt_khac': forms.TextInput(attrs={'class': 'form-control'}),
            'vb_phan_cong': forms.TextInput(attrs={'class': 'form-control'}),
            'thoi_gian_ngay': forms.TextInput(attrs={'class': 'form-control'}),
            'thoi_gian_tuan': forms.TextInput(attrs={'class': 'form-control'}),
            'cskcb_khac': forms.TextInput(attrs={'class': 'form-control'}),
            'cskcb_cgkt': forms.TextInput(attrs={'class': 'form-control'}),
            'qd_cgkt': forms.TextInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super(ProfileEditForm, self).__init__(*args, **kwargs)
        self.fields['department'].queryset = Department.objects.all().order_by('name')
        self.fields['position'].queryset = Position.objects.all().order_by('name')
        self.fields['pham_vi_cm'].queryset = PhamViChuyenMon.objects.all().order_by('ma_pham_vi')

        # Chuyển từ chuỗi phân cách ";" sang danh sách ID (str -> list[int])
        if self.instance and self.instance.pham_vi_cm:
            ma_list = self.instance.pham_vi_cm.split(';')
            self.initial['pham_vi_cm'] = PhamViChuyenMon.objects.filter(ma_pham_vi__in=ma_list)

        # Chuyển đổi các trường ngày dạng yyyyMMdd -> dd/MM/yyyy
        if self.instance:
            date_fields = ['tu_ngay', 'den_ngay', 'ngay_cap_cchn']
            for field in date_fields:
                raw_value = getattr(self.instance, field, "")
                self.initial[field] = parse_to_short_date(raw_value)

    def clean(self):
        cleaned_data = super().clean()
        for field in ['tu_ngay', 'den_ngay', 'ngay_cap_cchn']:
            original_value = cleaned_data.get(field)
            if original_value:  # Kiểm tra giá trị tồn tại
                formatted = parse_to_yyyyMMdd(original_value)
                if formatted and len(formatted) <= 8:
                    cleaned_data[field] = formatted
                else:
                    self.add_error(field, "Định dạng ngày không hợp lệ hoặc vượt quá 8 ký tự.")
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)

        selected_items = self.cleaned_data.get('pham_vi_cm')
        if selected_items:
            ma_list = [item.ma_pham_vi for item in selected_items]
            instance.pham_vi_cm = ';'.join(ma_list)
        else:
            instance.pham_vi_cm = ''

        if commit:
            instance.save()
        return instance
    
class UserPasswordChangeForm(PasswordChangeForm):
    def __init__(self, *args, **kwargs):
        super(UserPasswordChangeForm, self).__init__(*args, **kwargs)
        self.fields['old_password'].widget.attrs.update({'class': 'form-control', 'placeholder': 'Mật khẩu cũ'})
        self.fields['new_password1'].widget.attrs.update({'class': 'form-control', 'placeholder': 'Mật khẩu mới'})
        self.fields['new_password2'].widget.attrs.update({'class': 'form-control', 'placeholder': 'Xác nhận mật khẩu mới'})

class AdminPasswordChangeForm(SetPasswordForm):
    def __init__(self, *args, **kwargs):
        super(AdminPasswordChangeForm, self).__init__(*args, **kwargs)
        self.fields['new_password1'].widget.attrs.update({'class': 'form-control', 'placeholder': 'Mật khẩu mới'})
        self.fields['new_password2'].widget.attrs.update({'class': 'form-control', 'placeholder': 'Xác nhận mật khẩu mới'})

    def clean(self):
        cleaned_data = super().clean()
        password1 = cleaned_data.get('new_password1')
        password2 = cleaned_data.get('new_password2')

        if password1 and password2 and password1 != password2:
            self.add_error('new_password2', 'Mật khẩu xác nhận không khớp với mật khẩu mới.')

        return cleaned_data


class UserImportForm(forms.Form):
    file = forms.FileField(
        label='Chọn file Excel',
        help_text='Chỉ chấp nhận file Excel (.xlsx)',
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': '.xlsx'})
    )

    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            if not file.name.endswith('.xlsx'):
                raise forms.ValidationError('Chỉ chấp nhận file Excel (.xlsx)')
            return file
        return None

class PasswordResetRequestForm(forms.Form):
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Email'})
    )
