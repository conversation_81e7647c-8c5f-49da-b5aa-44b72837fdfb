from django import forms
from django.contrib.auth.models import User
from .models import Role, UserRole, Permission

class RoleForm(forms.ModelForm):
    """Form cho việc tạo và chỉnh sửa vai trò"""

    class Meta:
        model = Role
        fields = ['name', 'description', 'custom_permissions']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'custom_permissions': forms.SelectMultiple(attrs={'class': 'form-control select2', 'style': 'width: 100%'}),
        }

    def __init__(self, *args, **kwargs):
        super(RoleForm, self).__init__(*args, **kwargs)

        # <PERSON><PERSON><PERSON> t<PERSON><PERSON> cả custom permissions và sắp xếp theo module
        self.fields['custom_permissions'].queryset = Permission.objects.all().order_by('module', 'code')

        # Nhóm quyền theo module
        modules_dict = {}
        for permission in self.fields['custom_permissions'].queryset:
            module = permission.module

            if module not in modules_dict:
                modules_dict[module] = []

            modules_dict[module].append(permission)

        # Tạo danh sách quyền có nhóm theo module
        choices = []
        for module in sorted(modules_dict.keys()):
            module_group = []
            for permission in modules_dict[module]:
                module_group.append((permission.id, f"{permission.code} - {permission.name}"))

            if module_group:
                module_name = module.replace('_', ' ').title()
                choices.append((module_name, module_group))

        self.fields['custom_permissions'].choices = choices

class UserRoleForm(forms.ModelForm):
    """Form cho việc gán vai trò cho người dùng"""

    class Meta:
        model = UserRole
        fields = ['user', 'role']
        widgets = {
            'user': forms.Select(attrs={'class': 'form-control select2', 'style': 'width: 100%'}),
            'role': forms.Select(attrs={'class': 'form-control select2', 'style': 'width: 100%'}),
        }

    def __init__(self, *args, **kwargs):
        super(UserRoleForm, self).__init__(*args, **kwargs)
        self.fields['user'].queryset = User.objects.all().order_by('username')
        self.fields['role'].queryset = Role.objects.all().order_by('name')
