from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from permissions.models import Role
from django.shortcuts import get_object_or_404

@login_required
def role_permissions(request, role_id):
    """
    API endpoint để lấy danh s<PERSON>ch quyền của một vai trò
    """
    role = get_object_or_404(Role, id=role_id)
    permissions = role.custom_permissions.all()
    permission_ids = [p.id for p in permissions]

    return JsonResponse({
        'role_id': role.id,
        'role_name': role.name,
        'permissions': permission_ids
    })
