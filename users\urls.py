from django.urls import path
from . import views

app_name = 'users'

urlpatterns = [
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('password-reset/', views.password_reset, name='password_reset'),
    path('profile/', views.profile, name='profile'),
    path('profile/change-password/', views.change_password_self, name='change_password_self'),
    path('', views.user_list, name='list'),
    path('create/', views.user_edit, {'user_id': 0}, name='user_create'),
    path('<int:user_id>/', views.user_detail, name='user_detail'),
    path('<int:user_id>/edit/', views.user_edit, name='user_edit'),
    path('<int:user_id>/change-password/', views.change_password, name='change_password'),
    path('export/', views.export_users, name='export'),

    path('user-list-api/', views.user_list_api, name='user_list_api'),
    path("import-user-api/", views.import_users_api, name="import_users_api"),
    path('user-active-api/<int:pk>/toggle/', views.is_active_toogle_api, name='is_active_toogle_api'),
    path('user-delete-api/<int:pk>/delete', views.delete_user_api, name='delete_user_api'),
    path('user-update-api/<int:pk>/update', views.update_user_api, name='update_user_api'),
]
