# -*- coding: utf-8 -*-
"""
Management command để xóa các roles mặc định
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from permissions.models import Role, UserRole


class Command(BaseCommand):
    help = 'Xóa các roles mặc định đã tạo'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Xác nhận xóa tất cả roles',
        )

    def handle(self, *args, **options):
        confirm = options['confirm']

        if not confirm:
            self.stdout.write(
                self.style.WARNING('Cảnh báo: Lệnh này sẽ xóa TẤT CẢ roles và phân quyền người dùng!')
            )
            self.stdout.write('<PERSON><PERSON> thực hiện, hãy chạy lại với tham số --confirm')
            return

        self.stdout.write(
            self.style.SUCCESS('<PERSON><PERSON><PERSON> đầu xóa roles mặc định...')
        )

        try:
            with transaction.atomic():
                # Xóa tất cả UserRole trước
                user_role_count = UserRole.objects.count()
                UserRole.objects.all().delete()
                self.stdout.write(f'Đã xóa {user_role_count} phân quyền người dùng')

                # Xóa tất cả Role
                role_count = Role.objects.count()
                roles = Role.objects.all()
                
                for role in roles:
                    self.stdout.write(f'Xóa role: {role.name}')
                
                Role.objects.all().delete()
                self.stdout.write(f'Đã xóa {role_count} roles')

                # Hiển thị kết quả
                self.stdout.write('\n' + '='*50)
                self.stdout.write(
                    self.style.SUCCESS('Hoàn thành xóa roles mặc định!')
                )
                self.stdout.write(f'- Đã xóa {role_count} roles')
                self.stdout.write(f'- Đã xóa {user_role_count} phân quyền người dùng')

        except Exception as e:
            raise CommandError(f'Lỗi khi xóa roles: {str(e)}')
