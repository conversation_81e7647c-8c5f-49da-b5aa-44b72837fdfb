from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.contrib.auth.models import User, Permission as DjangoPermission
from django.contrib.contenttypes.models import ContentType
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.views.decorators.http import require_POST
import json
from .models import Role, UserRole, RoleTemplate, Permission
from .forms import RoleForm, UserRoleForm

def is_admin(user):
    """Kiểm tra xem người dùng có phải là quản trị viên không"""
    return user.is_superuser

@login_required
@user_passes_test(is_admin)
def role_list(request):
    """Hiển thị danh sách vai trò"""
    roles = Role.objects.all().order_by('name')

    # T<PERSON><PERSON> kiếm
    search_query = request.GET.get('search')
    if search_query:
        roles = roles.filter(name__icontains=search_query)

    # Phân trang
    paginator = Paginator(roles, 10)
    page = request.GET.get('page')
    roles = paginator.get_page(page)

    context = {
        'roles': roles,
        'search_query': search_query,
    }

    return render(request, 'permissions/role_list.html', context)

@login_required
@user_passes_test(is_admin)
def role_create(request):
    """Tạo vai trò mới"""
    if request.method == 'POST':
        # Kiểm tra xem có phải là vai trò mặc định không
        is_default = request.POST.get('is_default') == '1'
        default_type = request.POST.get('default_type')

        # Tạo vai trò mới
        role = Role(
            name=request.POST.get('name'),
            description=request.POST.get('description')
        )
        role.save()

        if is_default and default_type:
            # Xử lý các vai trò mặc định
            if default_type == 'superadmin':
                # Lấy tất cả quyền trong hệ thống
                all_permissions = Permission.objects.all()
                role.custom_permissions.set(all_permissions)
                messages.success(request, f'Vai trò Super Admin đã được tạo với tất cả quyền!')

            elif default_type == 'admin':
                # Lấy tất cả quyền ngoại trừ một số quyền nhạy cảm
                admin_permissions = Permission.objects.exclude(
                    code__in=['users.delete_superuser', 'permissions.delete_all_roles', 'system.full_access']
                )
                role.custom_permissions.set(admin_permissions)
                messages.success(request, f'Vai trò Admin đã được tạo với các quyền phù hợp!')

            elif default_type == 'department_manager':
                # Quyền quản lý khoa phòng
                dept_permissions = Permission.objects.filter(
                    Q(module='departments') |
                    Q(module='devices') |
                    Q(module='schedules') |
                    Q(code__startswith='users.view_') |
                    Q(code__startswith='typingpractice.')
                )
                role.custom_permissions.set(dept_permissions)
                messages.success(request, f'Vai trò Quản lý khoa phòng đã được tạo với các quyền phù hợp!')

            elif default_type == 'staff':
                # Quyền cơ bản cho nhân viên
                staff_permissions = Permission.objects.filter(
                    Q(code__startswith='users.view_') |
                    Q(code__startswith='devices.view_') |
                    Q(code__startswith='typingpractice.view_') |
                    Q(code__startswith='schedules.view_') |
                    Q(code='typingpractice.take_exam')
                )
                role.custom_permissions.set(staff_permissions)
                messages.success(request, f'Vai trò Nhân viên đã được tạo với các quyền cơ bản!')
        else:
            # Xử lý quyền từ checkbox cho vai trò tùy chỉnh (sử dụng custom permissions)
            permission_ids = request.POST.getlist('custom_permissions')
            permissions = Permission.objects.filter(id__in=permission_ids)
            role.custom_permissions.set(permissions)
            messages.success(request, f'Vai trò "{role.name}" đã được tạo thành công!')

        return redirect('permissions:role_list')
    else:
        form = RoleForm()

    # Lấy tất cả custom permissions và nhóm theo module
    permissions = Permission.objects.all().order_by('module', 'code')

    # Nhóm quyền theo module
    grouped_permissions = {}
    for permission in permissions:
        module_name = permission.module

        if module_name not in grouped_permissions:
            grouped_permissions[module_name] = []

        grouped_permissions[module_name].append(permission)

    selected_permissions = []

    context = {
        'form': form,
        'title': 'Tạo vai trò mới',
        'grouped_permissions': grouped_permissions,
        'selected_permissions': selected_permissions,
        'is_edit': False,
    }

    return render(request, 'permissions/role_form.html', context)

@login_required
@user_passes_test(is_admin)
def role_edit(request, pk):
    """Chỉnh sửa vai trò"""
    role = get_object_or_404(Role, pk=pk)

    if request.method == 'POST':
        # Xử lý quyền từ checkbox (sử dụng custom permissions)
        permission_ids = request.POST.getlist('custom_permissions')
        permissions = Permission.objects.filter(id__in=permission_ids)

        # Cập nhật thông tin cơ bản
        role.name = request.POST.get('name')
        role.description = request.POST.get('description')
        role.save()

        # Cập nhật quyền
        role.custom_permissions.set(permissions)

        messages.success(request, f'Vai trò "{role.name}" đã được cập nhật thành công!')
        return redirect('permissions:role_list')
    else:
        form = RoleForm(instance=role)

    # Lấy tất cả custom permissions và nhóm theo module
    permissions = Permission.objects.all().order_by('module', 'code')

    # Nhóm quyền theo module
    grouped_permissions = {}
    for permission in permissions:
        module_name = permission.module

        if module_name not in grouped_permissions:
            grouped_permissions[module_name] = []

        grouped_permissions[module_name].append(permission)

    selected_permissions = [p.pk for p in role.custom_permissions.all()]

    context = {
        'form': form,
        'role': role,
        'title': f'Chỉnh sửa vai trò: {role.name}',
        'grouped_permissions': grouped_permissions,
        'selected_permissions': selected_permissions,
        'is_edit': True,
    }

    return render(request, 'permissions/role_form.html', context)

@login_required
@user_passes_test(is_admin)
def role_delete(request, pk):
    """Xóa vai trò"""
    role = get_object_or_404(Role, pk=pk)

    if request.method == 'POST':
        role_name = role.name
        role.delete()
        messages.success(request, f'Vai trò "{role_name}" đã được xóa thành công!')
        return redirect('permissions:role_list')

    context = {
        'role': role,
    }

    return render(request, 'permissions/role_confirm_delete.html', context)

@login_required
@user_passes_test(is_admin)
def user_role_list(request):
    """Hiển thị danh sách phân quyền người dùng"""
    user_roles = UserRole.objects.all().select_related('user', 'role').order_by('user__username')

    # Tìm kiếm
    search_query = request.GET.get('search')
    if search_query:
        user_roles = user_roles.filter(user__username__icontains=search_query) | \
                    user_roles.filter(role__name__icontains=search_query)

    # Phân trang
    paginator = Paginator(user_roles, 10)
    page = request.GET.get('page')
    user_roles = paginator.get_page(page)

    context = {
        'user_roles': user_roles,
        'search_query': search_query,
    }

    return render(request, 'permissions/user_role_list.html', context)

@login_required
@user_passes_test(is_admin)
def user_role_create(request):
    """Tạo phân quyền người dùng mới"""
    if request.method == 'POST':
        user_id = request.POST.get('user')
        role_id = request.POST.get('role')

        if user_id and role_id:
            user = User.objects.get(id=user_id)
            role = Role.objects.get(id=role_id)

            # Kiểm tra xem phân quyền đã tồn tại chưa
            if not UserRole.objects.filter(user=user, role=role).exists():
                user_role = UserRole.objects.create(user=user, role=role)
                messages.success(request, f'Phân quyền cho người dùng "{user_role.user.username}" đã được tạo thành công!')
            else:
                messages.warning(request, f'Người dùng đã có vai trò này!')

            return redirect('permissions:user_role_list')
        else:
            messages.error(request, 'Vui lòng chọn người dùng và vai trò!')

    form = UserRoleForm()
    available_roles = Role.objects.all().order_by('name')

    context = {
        'form': form,
        'title': 'Tạo phân quyền người dùng mới',
        'available_roles': available_roles,
        'selected_role': None,
    }

    return render(request, 'permissions/user_role_form.html', context)

@login_required
@user_passes_test(is_admin)
def user_role_edit(request, pk):
    """Chỉnh sửa phân quyền người dùng"""
    user_role = get_object_or_404(UserRole, pk=pk)

    if request.method == 'POST':
        user_id = request.POST.get('user')
        role_id = request.POST.get('role')

        if user_id and role_id:
            # Kiểm tra xem phân quyền mới đã tồn tại chưa (nếu thay đổi vai trò)
            if int(role_id) != user_role.role.id and UserRole.objects.filter(user_id=user_id, role_id=role_id).exists():
                messages.warning(request, f'Người dùng đã có vai trò này!')
                return redirect('permissions:user_role_list')

            user_role.role_id = role_id
            user_role.save()

            messages.success(request, f'Phân quyền cho người dùng "{user_role.user.username}" đã được cập nhật thành công!')
            return redirect('permissions:user_role_list')
        else:
            messages.error(request, 'Vui lòng chọn vai trò!')

    form = UserRoleForm(instance=user_role)
    available_roles = Role.objects.all().order_by('name')

    context = {
        'form': form,
        'user_role': user_role,
        'title': f'Chỉnh sửa phân quyền: {user_role.user.username}',
        'available_roles': available_roles,
        'selected_role': user_role.role.id,
    }

    return render(request, 'permissions/user_role_form.html', context)

@login_required
@user_passes_test(is_admin)
def user_role_delete(request, pk):
    """Xóa phân quyền người dùng"""
    user_role = get_object_or_404(UserRole, pk=pk)

    if request.method == 'POST':
        username = user_role.user.username
        user_role.delete()
        messages.success(request, f'Phân quyền cho người dùng "{username}" đã được xóa thành công!')
        return redirect('permissions:user_role_list')

    context = {
        'user_role': user_role,
    }

    return render(request, 'permissions/user_role_confirm_delete.html', context)

@login_required
@user_passes_test(is_admin)
def role_template_manager(request):
    """Quản lý mẫu vai trò"""
    # Lấy tất cả custom permissions và nhóm theo module
    permissions = Permission.objects.all().order_by('module', 'code')

    # Nhóm quyền theo module
    grouped_permissions = {}
    for permission in permissions:
        module_name = permission.module

        if module_name not in grouped_permissions:
            grouped_permissions[module_name] = []

        grouped_permissions[module_name].append(permission)



    # Lấy quyền mặc định cho từng loại mẫu
    admin_permissions = [p.id for p in RoleTemplate.get_default_permissions('admin')]
    department_manager_permissions = [p.id for p in RoleTemplate.get_default_permissions('department_manager')]
    staff_permissions = [p.id for p in RoleTemplate.get_default_permissions('staff')]

    context = {
        'grouped_permissions': grouped_permissions,
        'admin_permissions': admin_permissions,
        'department_manager_permissions': department_manager_permissions,
        'staff_permissions': staff_permissions,
        'admin_default_permissions': admin_permissions,
        'department_manager_default_permissions': department_manager_permissions,
        'staff_default_permissions': staff_permissions,
    }

    return render(request, 'permissions/role_template_manager.html', context)

@login_required
@user_passes_test(is_admin)
@require_POST
def save_role_template(request, template_type):
    """Lưu mẫu vai trò"""
    if template_type not in dict(RoleTemplate.TEMPLATE_TYPES):
        messages.error(request, f'Loại mẫu không hợp lệ: {template_type}')
        return redirect('permissions:role_template_manager')

    # Lấy hoặc tạo mẫu vai trò
    template, created = RoleTemplate.objects.get_or_create(template_type=template_type)

    # Lấy danh sách quyền từ form
    permission_ids = request.POST.getlist('permissions')
    permissions = Permission.objects.filter(id__in=permission_ids)

    # Cập nhật quyền
    template.permissions.set(permissions)

    messages.success(request, f'Đã lưu mẫu vai trò {template.get_template_type_display()} thành công!')
    return redirect('permissions:role_template_manager')

@login_required
@user_passes_test(is_admin)
def reset_role_template(request, template_type):
    """Đặt lại mẫu vai trò về mặc định"""
    if template_type not in dict(RoleTemplate.TEMPLATE_TYPES):
        messages.error(request, f'Loại mẫu không hợp lệ: {template_type}')
        return redirect('permissions:role_template_manager')

    # Lấy hoặc tạo mẫu vai trò
    template, created = RoleTemplate.objects.get_or_create(template_type=template_type)

    # Lấy quyền mặc định
    default_permissions = RoleTemplate.get_default_permissions(template_type)

    # Cập nhật quyền
    template.permissions.set(default_permissions)

    messages.success(request, f'Đã đặt lại mẫu vai trò {template.get_template_type_display()} về mặc định!')
    return redirect('permissions:role_template_manager')

@login_required
def get_template_permissions(request):
    """API để lấy quyền từ mẫu vai trò"""
    template_type = request.GET.get('template_type')

    if not template_type or template_type not in dict(RoleTemplate.TEMPLATE_TYPES):
        return JsonResponse({
            'success': False,
            'message': f'Loại mẫu không hợp lệ: {template_type}'
        })

    # Kiểm tra xem mẫu đã tồn tại trong cơ sở dữ liệu chưa
    template = RoleTemplate.objects.filter(template_type=template_type).first()

    if template and template.permissions.exists():
        # Nếu mẫu đã tồn tại và có quyền, sử dụng quyền từ cơ sở dữ liệu
        permissions = [p.id for p in template.permissions.all()]
        template_name = template.get_template_type_display()
    else:
        # Nếu mẫu chưa tồn tại hoặc không có quyền, sử dụng quyền mặc định
        permissions = [p.id for p in RoleTemplate.get_default_permissions(template_type)]
        template_name = dict(RoleTemplate.TEMPLATE_TYPES).get(template_type)

    return JsonResponse({
        'success': True,
        'template_type': template_type,
        'template_name': template_name,
        'permissions': permissions
    })
