{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}<PERSON><PERSON> sách người dùng{% endblock %}
{% block page_title %}<PERSON>h sách người dùng{% endblock %}

{% block extra_css %}
<link href="{% static "tabulator/dist/css/tabulator.min.css" %}" rel="stylesheet">
<link href="{% static "tabulator/dist/css/tabulator_bootstrap4.min.css" %}" rel="stylesheet">
<style>
  #user-table {
      height: 600px;
  }
  .switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 24px;
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
  }

  .slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
  }

  input:checked + .slider {
    background-color: #28a745;
  }

  input:checked + .slider:before {
    transform: translateX(16px);
  }
</style>
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center w-100">
          <div class="d-flex align-items-center">
            <div class="btn-group">
              <button class="btn btn-primary btn-sm mr-2" id="btn-create-user">
                <i class="fas fa-plus"></i> Thêm người dùng
              </button>

              <div class="custom-file mr-2" style="width: 250px;">
                <input type="file" class="custom-file-input" id="import-file" accept=".xls,.xlsx">
                <label class="custom-file-label" for="import-file">Chọn tệp</label>
              </div>

              <button class="btn btn-success btn-sm mr-2" id="btn-import">
                <i class="fas fa-file-import"></i> Nhập Excel
              </button>
            
              <div class="btn-group">
                <button type="button" class="btn btn-info" onclick="exportUsers('all')">
                  <i class="fas fa-file-excel"></i> Xuất Excel
                </button>
                <button type="button" class="btn btn-info dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-expanded="false">
                  <span class="sr-only">Toggle Dropdown</span>
                </button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="#" onclick="exportUsers('all')">
                    <i class="fas fa-users"></i> Xuất toàn bộ nhân viên
                  </a>
                  <a class="dropdown-item" href="#" onclick="exportUsers('cchn')">
                    <i class="fas fa-certificate"></i> Xuất chỉ có CCHN
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div id="user-table"></div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static "tabulator/dist/js/tabulator.min.js" %}"></script>
<script src="{% static "node_modules/xlsx/dist/xlsx.full.min.js" %}"></script>
<script>
document.addEventListener("DOMContentLoaded", function () {
    const csrfToken = "{{ csrf_token }}";
    const table = new Tabulator("#user-table", {
        layout: "fitDataFill",
        ajaxURL: "{% url 'users:user_list_api' %}",
        pagination: "local",
        paginationSize: 10,
        headerFilterLiveFilterDelay: 300,
        resizableColumnFit: true,
        columns: [
            { title: "Tài khoản", field: "username", headerFilter: true },
            { title: "Họ tên", field: "first_name", headerFilter: true },
            { title: "Chứng chỉ hành nghề", field: "practice_certificate", headerFilter: true },
            { title: "Mã Khoa", field: "ma_khoa", headerFilter: true },
            { title: "Khoa làm việc", field: "department", },
            { title: "Mã loại KCB", field: "ma_loai_kcb",
                formatter: function(cell) {
                  const labels = {
                    0:"",
                    1: "Khám bệnh, chữa bệnh ngoại trú",
                    4: "Điều trị nội trú",
                    2: "Bệnh viện chuyên khoa được cấp có thẩm quyền phê duyệt thành lập liên khoa",
                  };
                  return labels[cell.getValue()] || "";
                }
            },
            { title: "Mã khoa BHXH", field: "ma_khoa_bhxh"},
            
            { title: "Giới tính", field: "gioi_tinh",
              formatter: function(cell) {
                const val = cell.getValue();
                return val == 1 ? "Nam" : val == 2 ? "Nữ" : "";
              }
            },
            { title: "Chức danh NN", field: "chuc_danh_nn" },
            { title: "Vị trí", field: "vi_tri" },
            { title: "Ngày cấp CCHN", field: "ngay_cap_cchn" },
            { title: "Nơi cấp CCHN", field: "noi_cap_cchn" },
            { title: "Phạm vi CM", field: "pham_vi_cm" },
            { title: "Phạm vi chuyên môn bs", field: "pham_vi_cm_bs" },
            { title: "DVKT khác", field: "dvkt_khac" },
            { title: "Văn bản phân công", field: "vb_phan_cong" },
            { title: "Thời gian đăng kí", field: "thoi_gian_dk" },
            { title: "Thời gian ngày", field: "thoi_gian_ngay" },
            { title: "Thời gian tuần", field: "thoi_gian_tuan" },
            { title: "CSKCB khác", field: "cskcb_khac" },
            { title: "CSKCB CGKT", field: "cskcb_cgkt" },
            { title: "Quyết định CGKT", field: "qd_cgkt" },
            { title: "Từ ngày", field: "tu_ngay" },
            { title: "Đến ngày", field: "den_ngay" },
            { title: "Trạng thái", field: "is_active", hozAlign: "center",
              formatter: function(cell) {
                const checked = cell.getValue() ? "checked" : "";
                return `<label class="switch">
                          <input type="checkbox" class="toggle-switch" data-id="${cell.getRow().getData().id}" ${checked}>
                          <span class="slider round"></span>
                        </label>`;
              },
              cellClick: function(e, cell) {
                const checkbox = e.target.closest(".toggle-switch");
                if (checkbox) {
                  const userId = checkbox.dataset.id;
                  const isChecked = checkbox.checked;
                  fetch("{% url 'users:is_active_toogle_api' 0 %}".replace("0", userId), {
                    method: "POST",
                    headers: {
                      "X-CSRFToken": csrfToken,
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify({})
                  })
                  .then(response => response.json())
                  .then(data => {
                    if (data.status === 'success') {
                      cell.setValue(data.is_used);
                    } else {
                      checkbox.checked = !isChecked;
                      Swal.fire("Lỗi", "Không thể cập nhật trạng thái!", "error");
                    }
                  })
                  .catch(() => {
                    checkbox.checked = !isChecked;
                    Swal.fire("Lỗi", "Kết nối thất bại!", "error");
                  });
                }
              }
            },
            { title: "Thao tác", field: "actions", frozen: true,
              formatter: function(cell) {
                const edited = cell.getRow().getData()._edited;
                return `
                  <button class="btn btn-sm btn-info view-edit-btn">Xem</button>
                  <button class="btn btn-sm btn-danger delete-btn">Xóa</button>
                  `;
              },
              cellClick: function(e, cell) {
                const row = cell.getRow();
                const data = row.getData();
                if (e.target.classList.contains("delete-btn")) {
                  Swal.fire({
                    title: `Xóa ${data.first_name}?`,
                    text: "Hành động này không thể hoàn tác!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonText: "Xóa",
                    cancelButtonText: "Hủy"
                  }).then(result => {
                    if (result.isConfirmed) {
                      fetch("{% url 'users:delete_user_api' 0 %}".replace("0", data.id), {
                        method: "POST",
                        headers: {
                          "X-CSRFToken": csrfToken,
                        }
                      })
                      .then(resp => resp.json())
                      .then(r => {
                        if (r.status === 'success') {
                          row.delete();
                          Swal.fire("Đã xóa!", "Dữ liệu đã được xóa.", "success");
                        } else {
                          Swal.fire("Lỗi", "Không thể xóa dữ liệu!", "error");
                        }
                      });
                    }
                  });
                }
                if (e.target.classList.contains("view-edit-btn")) {
                  const row = cell.getRow();
                  const data = row.getData();
                  window.location.href = "{% url 'users:user_edit' 0 %}".replace("0", data.id);
                }
              }
            }
        ],
        placeholder: "Chưa có dữ liệu",
    });

    // Tạo người dùng mới
    document.getElementById("btn-create-user").addEventListener("click", () => {
      window.location.href = "{% url 'users:user_create' %}";
    });

    // Script để hiển thị tên file
    document.getElementById('import-file').addEventListener('change', function (e) {
      const file = e.target.files[0];
      if (file) {
        const fileName = file.name.toLowerCase();
        const isExcel = fileName.endsWith('.xls') || fileName.endsWith('.xlsx');
        if (!isExcel) {
          Swal.fire({
            icon: 'error',
            title: 'Tệp không hợp lệ',
            text: 'Vui lòng chọn tệp Excel (.xls hoặc .xlsx)',
            confirmButtonText: 'Đã hiểu',
          });

          e.target.value = '';  // Xóa file không hợp lệ
          e.target.nextElementSibling.innerText = 'Chọn tệp';  // Reset label
          return;
        }

        // Nếu hợp lệ, hiển thị tên file
        e.target.nextElementSibling.innerText = file.name;
      }
    });

    // Đọc file Excel
    document.getElementById("btn-import").addEventListener("click", () => {
        const fileInput = document.getElementById("import-file");
        const file = fileInput.files[0];
        if (!file) {
            Swal.fire("Thông báo", "Vui lòng chọn file Excel để nhập!", "warning");
            return;
        }

        const reader = new FileReader();
        reader.onload = function (e) {
            Swal.fire({
              title: 'Đang nhập dữ liệu...',
              allowOutsideClick: false,
              didOpen: () => {
                Swal.showLoading();
              }
            });

            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: "array" });
            const firstSheet = workbook.SheetNames[0];
            const sheetData = XLSX.utils.sheet_to_json(workbook.Sheets[firstSheet], { defval: "" });

            if (sheetData.length === 0) {
                Swal.fire("Lỗi", "File không có dữ liệu!", "error");
                return;
            }

            table.setData(sheetData);

            // Gửi dữ liệu về server nếu muốn
            fetch("{% url 'users:import_users_api' %}", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRFToken": "{{ csrf_token }}",
                },
                body: JSON.stringify({ data: sheetData }),
            })
            .then((res) => res.json())
            .then((res) => {
                if (res.success) {
                  Swal.fire({
                    icon: 'success',
                    title: 'Nhập dữ liệu thành công',
                    showConfirmButton: false,
                    timer: 1500
                  }).then(() => {
                    location.reload();  // Reload lại trang để load lại từ server
                  })
                } else {
                  Swal.fire("Lỗi", res.message || "Lỗi không xác định", "error");
                }
            })
            .catch(() => {
                Swal.fire("Lỗi", "Không thể gửi dữ liệu về server", "error");
            });
        };

        reader.readAsArrayBuffer(file);
    });
});

// Function để export users
function exportUsers(type) {
    // Hiển thị loading
    Swal.fire({
        title: 'Đang xuất dữ liệu...',
        text: type === 'cchn' ? 'Đang xuất danh sách nhân viên có CCHN...' : 'Đang xuất toàn bộ danh sách nhân viên...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // Tạo URL với tham số type
    const exportUrl = "{% url 'users:export' %}" + "?type=" + type;

    // Tạo link ẩn để download
    const link = document.createElement('a');
    link.href = exportUrl;
    link.style.display = 'none';
    document.body.appendChild(link);

    // Trigger download
    link.click();

    // Cleanup
    document.body.removeChild(link);

    // Đóng loading sau 1 giây
    setTimeout(() => {
        Swal.close();
        Swal.fire({
            icon: 'success',
            title: 'Xuất dữ liệu thành công!',
            text: type === 'cchn' ? 'Đã xuất danh sách nhân viên có CCHN' : 'Đã xuất toàn bộ danh sách nhân viên',
            timer: 2000,
            showConfirmButton: false
        });
    }, 1000);
}
</script>
{% endblock %}
